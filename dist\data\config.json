{"app": {"name": "MarketPlace Local", "tagline": "Votre marketplace de proximité", "description": "Découvrez les meilleurs produits et services locaux près de chez vous", "version": "1.0.0", "logo": "/images/logo.jpg", "defaultLocation": {"lat": 14.6928, "lng": -17.4467, "city": "<PERSON><PERSON>", "country": "Sénégal"}}, "categories": [{"id": "alimentation", "name": "Alimentation", "icon": "🍎", "color": "#10B981", "description": "Fruits, légumes et produits frais"}, {"id": "beaute_soins", "name": "Beauté & Soins", "icon": "💄", "color": "#F97316", "description": "Salons, soins et produits de beauté"}, {"id": "mode_textile", "name": "Mode & Textile", "icon": "👗", "color": "#8B5CF6", "description": "Vêtements, couture et accessoires"}, {"id": "restauration", "name": "Restauration", "icon": "🍽️", "color": "#EF4444", "description": "Restaurants et plats traditionnels"}, {"id": "services", "name": "Services", "icon": "🔧", "color": "#1E40AF", "description": "Services et réparations"}], "paymentMethods": [{"id": "orange_money", "name": "Orange Money", "icon": "📱", "color": "#FF6600", "isActive": true}, {"id": "wave", "name": "Wave", "icon": "💳", "color": "#0066CC", "isActive": true}, {"id": "cash", "name": "Espèces", "icon": "💵", "color": "#10B981", "isActive": true}], "deliveryZones": [{"id": "dakar_centre", "name": "Dakar Centre", "fee": 500, "radius": 5, "estimatedTime": "30-45 min"}, {"id": "banlieue", "name": "<PERSON><PERSON><PERSON> <PERSON>", "fee": 1000, "radius": 15, "estimatedTime": "45-90 min"}, {"id": "region_dakar", "name": "Région de Dakar", "fee": 2000, "radius": 30, "estimatedTime": "90-120 min"}], "orderStatuses": [{"id": "pending", "name": "En attente", "color": "#F59E0B", "description": "Commande en attente de confirmation"}, {"id": "confirmed", "name": "Confirmée", "color": "#3B82F6", "description": "Commande confirmée par le vendeur"}, {"id": "prepared", "name": "En préparation", "color": "#8B5CF6", "description": "Commande en cours de préparation"}, {"id": "in_production", "name": "En production", "color": "#6366F1", "description": "Article en cours de fabrication"}, {"id": "shipped", "name": "Expédiée", "color": "#06B6D4", "description": "Commande en cours de livraison"}, {"id": "in_transit", "name": "En transit", "color": "#06B6D4", "description": "Commande en cours de transport"}, {"id": "delivered", "name": "Livrée", "color": "#10B981", "description": "Commande livrée avec succès"}, {"id": "completed", "name": "Terminée", "color": "#10B981", "description": "Service/commande complètement terminé"}, {"id": "cancelled", "name": "<PERSON><PERSON><PERSON>", "color": "#EF4444", "description": "Commande annulée"}], "notifications": {"orderConfirmed": "Votre commande a été confirmée !", "orderShipped": "Votre commande est en route !", "orderDelivered": "Votre commande a été livrée !", "newVendorNearby": "Nouveau vendeur dans votre zone !", "specialOffer": "Offre spéciale disponible !"}, "contact": {"email": "<EMAIL>", "phone": "+221 33 123 45 67", "whatsapp": "+221 77 123 45 67", "address": "Dakar, Sénégal"}, "social": {"facebook": "https://facebook.com/marketplacelocal", "twitter": "https://twitter.com/marketplacelocal", "instagram": "https://instagram.com/marketplacelocal"}}