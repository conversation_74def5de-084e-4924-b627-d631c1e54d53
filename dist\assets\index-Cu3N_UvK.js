function hy(n,r){for(var s=0;s<r.length;s++){const a=r[s];if(typeof a!="string"&&!Array.isArray(a)){for(const u in a)if(u!=="default"&&!(u in n)){const d=Object.getOwnPropertyDescriptor(a,u);d&&Object.defineProperty(n,u,d.get?d:{enumerable:!0,get:()=>a[u]})}}}return Object.freeze(Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}))}(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const u of document.querySelectorAll('link[rel="modulepreload"]'))a(u);new MutationObserver(u=>{for(const d of u)if(d.type==="childList")for(const f of d.addedNodes)f.tagName==="LINK"&&f.rel==="modulepreload"&&a(f)}).observe(document,{childList:!0,subtree:!0});function s(u){const d={};return u.integrity&&(d.integrity=u.integrity),u.referrerPolicy&&(d.referrerPolicy=u.referrerPolicy),u.crossOrigin==="use-credentials"?d.credentials="include":u.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function a(u){if(u.ep)return;u.ep=!0;const d=s(u);fetch(u.href,d)}})();function py(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var ml={exports:{}},pi={},gl={exports:{}},oe={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _f;function my(){if(_f)return oe;_f=1;var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),d=Symbol.for("react.provider"),f=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),x=Symbol.iterator;function w(P){return P===null||typeof P!="object"?null:(P=x&&P[x]||P["@@iterator"],typeof P=="function"?P:null)}var S={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},A=Object.assign,D={};function M(P,_,ie){this.props=P,this.context=_,this.refs=D,this.updater=ie||S}M.prototype.isReactComponent={},M.prototype.setState=function(P,_){if(typeof P!="object"&&typeof P!="function"&&P!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,P,_,"setState")},M.prototype.forceUpdate=function(P){this.updater.enqueueForceUpdate(this,P,"forceUpdate")};function L(){}L.prototype=M.prototype;function z(P,_,ie){this.props=P,this.context=_,this.refs=D,this.updater=ie||S}var F=z.prototype=new L;F.constructor=z,A(F,M.prototype),F.isPureReactComponent=!0;var Y=Array.isArray,U=Object.prototype.hasOwnProperty,X={current:null},re={key:!0,ref:!0,__self:!0,__source:!0};function Z(P,_,ie){var ae,ce={},de=null,ge=null;if(_!=null)for(ae in _.ref!==void 0&&(ge=_.ref),_.key!==void 0&&(de=""+_.key),_)U.call(_,ae)&&!re.hasOwnProperty(ae)&&(ce[ae]=_[ae]);var he=arguments.length-2;if(he===1)ce.children=ie;else if(1<he){for(var Se=Array(he),ct=0;ct<he;ct++)Se[ct]=arguments[ct+2];ce.children=Se}if(P&&P.defaultProps)for(ae in he=P.defaultProps,he)ce[ae]===void 0&&(ce[ae]=he[ae]);return{$$typeof:n,type:P,key:de,ref:ge,props:ce,_owner:X.current}}function me(P,_){return{$$typeof:n,type:P.type,key:_,ref:P.ref,props:P.props,_owner:P._owner}}function we(P){return typeof P=="object"&&P!==null&&P.$$typeof===n}function qe(P){var _={"=":"=0",":":"=2"};return"$"+P.replace(/[=:]/g,function(ie){return _[ie]})}var ut=/\/+/g;function Qe(P,_){return typeof P=="object"&&P!==null&&P.key!=null?qe(""+P.key):_.toString(36)}function Je(P,_,ie,ae,ce){var de=typeof P;(de==="undefined"||de==="boolean")&&(P=null);var ge=!1;if(P===null)ge=!0;else switch(de){case"string":case"number":ge=!0;break;case"object":switch(P.$$typeof){case n:case r:ge=!0}}if(ge)return ge=P,ce=ce(ge),P=ae===""?"."+Qe(ge,0):ae,Y(ce)?(ie="",P!=null&&(ie=P.replace(ut,"$&/")+"/"),Je(ce,_,ie,"",function(ct){return ct})):ce!=null&&(we(ce)&&(ce=me(ce,ie+(!ce.key||ge&&ge.key===ce.key?"":(""+ce.key).replace(ut,"$&/")+"/")+P)),_.push(ce)),1;if(ge=0,ae=ae===""?".":ae+":",Y(P))for(var he=0;he<P.length;he++){de=P[he];var Se=ae+Qe(de,he);ge+=Je(de,_,ie,Se,ce)}else if(Se=w(P),typeof Se=="function")for(P=Se.call(P),he=0;!(de=P.next()).done;)de=de.value,Se=ae+Qe(de,he++),ge+=Je(de,_,ie,Se,ce);else if(de==="object")throw _=String(P),Error("Objects are not valid as a React child (found: "+(_==="[object Object]"?"object with keys {"+Object.keys(P).join(", ")+"}":_)+"). If you meant to render a collection of children, use an array instead.");return ge}function Tt(P,_,ie){if(P==null)return P;var ae=[],ce=0;return Je(P,ae,"","",function(de){return _.call(ie,de,ce++)}),ae}function Ge(P){if(P._status===-1){var _=P._result;_=_(),_.then(function(ie){(P._status===0||P._status===-1)&&(P._status=1,P._result=ie)},function(ie){(P._status===0||P._status===-1)&&(P._status=2,P._result=ie)}),P._status===-1&&(P._status=0,P._result=_)}if(P._status===1)return P._result.default;throw P._result}var se={current:null},B={transition:null},q={ReactCurrentDispatcher:se,ReactCurrentBatchConfig:B,ReactCurrentOwner:X};function W(){throw Error("act(...) is not supported in production builds of React.")}return oe.Children={map:Tt,forEach:function(P,_,ie){Tt(P,function(){_.apply(this,arguments)},ie)},count:function(P){var _=0;return Tt(P,function(){_++}),_},toArray:function(P){return Tt(P,function(_){return _})||[]},only:function(P){if(!we(P))throw Error("React.Children.only expected to receive a single React element child.");return P}},oe.Component=M,oe.Fragment=s,oe.Profiler=u,oe.PureComponent=z,oe.StrictMode=a,oe.Suspense=p,oe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=q,oe.act=W,oe.cloneElement=function(P,_,ie){if(P==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+P+".");var ae=A({},P.props),ce=P.key,de=P.ref,ge=P._owner;if(_!=null){if(_.ref!==void 0&&(de=_.ref,ge=X.current),_.key!==void 0&&(ce=""+_.key),P.type&&P.type.defaultProps)var he=P.type.defaultProps;for(Se in _)U.call(_,Se)&&!re.hasOwnProperty(Se)&&(ae[Se]=_[Se]===void 0&&he!==void 0?he[Se]:_[Se])}var Se=arguments.length-2;if(Se===1)ae.children=ie;else if(1<Se){he=Array(Se);for(var ct=0;ct<Se;ct++)he[ct]=arguments[ct+2];ae.children=he}return{$$typeof:n,type:P.type,key:ce,ref:de,props:ae,_owner:ge}},oe.createContext=function(P){return P={$$typeof:f,_currentValue:P,_currentValue2:P,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},P.Provider={$$typeof:d,_context:P},P.Consumer=P},oe.createElement=Z,oe.createFactory=function(P){var _=Z.bind(null,P);return _.type=P,_},oe.createRef=function(){return{current:null}},oe.forwardRef=function(P){return{$$typeof:h,render:P}},oe.isValidElement=we,oe.lazy=function(P){return{$$typeof:y,_payload:{_status:-1,_result:P},_init:Ge}},oe.memo=function(P,_){return{$$typeof:g,type:P,compare:_===void 0?null:_}},oe.startTransition=function(P){var _=B.transition;B.transition={};try{P()}finally{B.transition=_}},oe.unstable_act=W,oe.useCallback=function(P,_){return se.current.useCallback(P,_)},oe.useContext=function(P){return se.current.useContext(P)},oe.useDebugValue=function(){},oe.useDeferredValue=function(P){return se.current.useDeferredValue(P)},oe.useEffect=function(P,_){return se.current.useEffect(P,_)},oe.useId=function(){return se.current.useId()},oe.useImperativeHandle=function(P,_,ie){return se.current.useImperativeHandle(P,_,ie)},oe.useInsertionEffect=function(P,_){return se.current.useInsertionEffect(P,_)},oe.useLayoutEffect=function(P,_){return se.current.useLayoutEffect(P,_)},oe.useMemo=function(P,_){return se.current.useMemo(P,_)},oe.useReducer=function(P,_,ie){return se.current.useReducer(P,_,ie)},oe.useRef=function(P){return se.current.useRef(P)},oe.useState=function(P){return se.current.useState(P)},oe.useSyncExternalStore=function(P,_,ie){return se.current.useSyncExternalStore(P,_,ie)},oe.useTransition=function(){return se.current.useTransition()},oe.version="18.3.1",oe}var bf;function ru(){return bf||(bf=1,gl.exports=my()),gl.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Of;function gy(){if(Of)return pi;Of=1;var n=ru(),r=Symbol.for("react.element"),s=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,u=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,d={key:!0,ref:!0,__self:!0,__source:!0};function f(h,p,g){var y,x={},w=null,S=null;g!==void 0&&(w=""+g),p.key!==void 0&&(w=""+p.key),p.ref!==void 0&&(S=p.ref);for(y in p)a.call(p,y)&&!d.hasOwnProperty(y)&&(x[y]=p[y]);if(h&&h.defaultProps)for(y in p=h.defaultProps,p)x[y]===void 0&&(x[y]=p[y]);return{$$typeof:r,type:h,key:w,ref:S,props:x,_owner:u.current}}return pi.Fragment=s,pi.jsx=f,pi.jsxs=f,pi}var Ff;function yy(){return Ff||(Ff=1,ml.exports=gy()),ml.exports}var v=yy(),C=ru();const Sp=py(C),vy=hy({__proto__:null,default:Sp},[C]);var Hs={},yl={exports:{}},st={},vl={exports:{}},xl={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var If;function xy(){return If||(If=1,function(n){function r(B,q){var W=B.length;B.push(q);e:for(;0<W;){var P=W-1>>>1,_=B[P];if(0<u(_,q))B[P]=q,B[W]=_,W=P;else break e}}function s(B){return B.length===0?null:B[0]}function a(B){if(B.length===0)return null;var q=B[0],W=B.pop();if(W!==q){B[0]=W;e:for(var P=0,_=B.length,ie=_>>>1;P<ie;){var ae=2*(P+1)-1,ce=B[ae],de=ae+1,ge=B[de];if(0>u(ce,W))de<_&&0>u(ge,ce)?(B[P]=ge,B[de]=W,P=de):(B[P]=ce,B[ae]=W,P=ae);else if(de<_&&0>u(ge,W))B[P]=ge,B[de]=W,P=de;else break e}}return q}function u(B,q){var W=B.sortIndex-q.sortIndex;return W!==0?W:B.id-q.id}if(typeof performance=="object"&&typeof performance.now=="function"){var d=performance;n.unstable_now=function(){return d.now()}}else{var f=Date,h=f.now();n.unstable_now=function(){return f.now()-h}}var p=[],g=[],y=1,x=null,w=3,S=!1,A=!1,D=!1,M=typeof setTimeout=="function"?setTimeout:null,L=typeof clearTimeout=="function"?clearTimeout:null,z=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function F(B){for(var q=s(g);q!==null;){if(q.callback===null)a(g);else if(q.startTime<=B)a(g),q.sortIndex=q.expirationTime,r(p,q);else break;q=s(g)}}function Y(B){if(D=!1,F(B),!A)if(s(p)!==null)A=!0,Ge(U);else{var q=s(g);q!==null&&se(Y,q.startTime-B)}}function U(B,q){A=!1,D&&(D=!1,L(Z),Z=-1),S=!0;var W=w;try{for(F(q),x=s(p);x!==null&&(!(x.expirationTime>q)||B&&!qe());){var P=x.callback;if(typeof P=="function"){x.callback=null,w=x.priorityLevel;var _=P(x.expirationTime<=q);q=n.unstable_now(),typeof _=="function"?x.callback=_:x===s(p)&&a(p),F(q)}else a(p);x=s(p)}if(x!==null)var ie=!0;else{var ae=s(g);ae!==null&&se(Y,ae.startTime-q),ie=!1}return ie}finally{x=null,w=W,S=!1}}var X=!1,re=null,Z=-1,me=5,we=-1;function qe(){return!(n.unstable_now()-we<me)}function ut(){if(re!==null){var B=n.unstable_now();we=B;var q=!0;try{q=re(!0,B)}finally{q?Qe():(X=!1,re=null)}}else X=!1}var Qe;if(typeof z=="function")Qe=function(){z(ut)};else if(typeof MessageChannel<"u"){var Je=new MessageChannel,Tt=Je.port2;Je.port1.onmessage=ut,Qe=function(){Tt.postMessage(null)}}else Qe=function(){M(ut,0)};function Ge(B){re=B,X||(X=!0,Qe())}function se(B,q){Z=M(function(){B(n.unstable_now())},q)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(B){B.callback=null},n.unstable_continueExecution=function(){A||S||(A=!0,Ge(U))},n.unstable_forceFrameRate=function(B){0>B||125<B?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):me=0<B?Math.floor(1e3/B):5},n.unstable_getCurrentPriorityLevel=function(){return w},n.unstable_getFirstCallbackNode=function(){return s(p)},n.unstable_next=function(B){switch(w){case 1:case 2:case 3:var q=3;break;default:q=w}var W=w;w=q;try{return B()}finally{w=W}},n.unstable_pauseExecution=function(){},n.unstable_requestPaint=function(){},n.unstable_runWithPriority=function(B,q){switch(B){case 1:case 2:case 3:case 4:case 5:break;default:B=3}var W=w;w=B;try{return q()}finally{w=W}},n.unstable_scheduleCallback=function(B,q,W){var P=n.unstable_now();switch(typeof W=="object"&&W!==null?(W=W.delay,W=typeof W=="number"&&0<W?P+W:P):W=P,B){case 1:var _=-1;break;case 2:_=250;break;case 5:_=**********;break;case 4:_=1e4;break;default:_=5e3}return _=W+_,B={id:y++,callback:q,priorityLevel:B,startTime:W,expirationTime:_,sortIndex:-1},W>P?(B.sortIndex=W,r(g,B),s(p)===null&&B===s(g)&&(D?(L(Z),Z=-1):D=!0,se(Y,W-P))):(B.sortIndex=_,r(p,B),A||S||(A=!0,Ge(U))),B},n.unstable_shouldYield=qe,n.unstable_wrapCallback=function(B){var q=w;return function(){var W=w;w=q;try{return B.apply(this,arguments)}finally{w=W}}}}(xl)),xl}var Bf;function wy(){return Bf||(Bf=1,vl.exports=xy()),vl.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zf;function Sy(){if(zf)return st;zf=1;var n=ru(),r=wy();function s(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,i=1;i<arguments.length;i++)t+="&args[]="+encodeURIComponent(arguments[i]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var a=new Set,u={};function d(e,t){f(e,t),f(e+"Capture",t)}function f(e,t){for(u[e]=t,e=0;e<t.length;e++)a.add(t[e])}var h=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),p=Object.prototype.hasOwnProperty,g=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,y={},x={};function w(e){return p.call(x,e)?!0:p.call(y,e)?!1:g.test(e)?x[e]=!0:(y[e]=!0,!1)}function S(e,t,i,o){if(i!==null&&i.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return o?!1:i!==null?!i.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function A(e,t,i,o){if(t===null||typeof t>"u"||S(e,t,i,o))return!0;if(o)return!1;if(i!==null)switch(i.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function D(e,t,i,o,l,c,m){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=o,this.attributeNamespace=l,this.mustUseProperty=i,this.propertyName=e,this.type=t,this.sanitizeURL=c,this.removeEmptyString=m}var M={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){M[e]=new D(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];M[t]=new D(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){M[e]=new D(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){M[e]=new D(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){M[e]=new D(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){M[e]=new D(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){M[e]=new D(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){M[e]=new D(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){M[e]=new D(e,5,!1,e.toLowerCase(),null,!1,!1)});var L=/[\-:]([a-z])/g;function z(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(L,z);M[t]=new D(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(L,z);M[t]=new D(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(L,z);M[t]=new D(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){M[e]=new D(e,1,!1,e.toLowerCase(),null,!1,!1)}),M.xlinkHref=new D("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){M[e]=new D(e,1,!1,e.toLowerCase(),null,!0,!0)});function F(e,t,i,o){var l=M.hasOwnProperty(t)?M[t]:null;(l!==null?l.type!==0:o||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(A(t,i,l,o)&&(i=null),o||l===null?w(t)&&(i===null?e.removeAttribute(t):e.setAttribute(t,""+i)):l.mustUseProperty?e[l.propertyName]=i===null?l.type===3?!1:"":i:(t=l.attributeName,o=l.attributeNamespace,i===null?e.removeAttribute(t):(l=l.type,i=l===3||l===4&&i===!0?"":""+i,o?e.setAttributeNS(o,t,i):e.setAttribute(t,i))))}var Y=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,U=Symbol.for("react.element"),X=Symbol.for("react.portal"),re=Symbol.for("react.fragment"),Z=Symbol.for("react.strict_mode"),me=Symbol.for("react.profiler"),we=Symbol.for("react.provider"),qe=Symbol.for("react.context"),ut=Symbol.for("react.forward_ref"),Qe=Symbol.for("react.suspense"),Je=Symbol.for("react.suspense_list"),Tt=Symbol.for("react.memo"),Ge=Symbol.for("react.lazy"),se=Symbol.for("react.offscreen"),B=Symbol.iterator;function q(e){return e===null||typeof e!="object"?null:(e=B&&e[B]||e["@@iterator"],typeof e=="function"?e:null)}var W=Object.assign,P;function _(e){if(P===void 0)try{throw Error()}catch(i){var t=i.stack.trim().match(/\n( *(at )?)/);P=t&&t[1]||""}return`
`+P+e}var ie=!1;function ae(e,t){if(!e||ie)return"";ie=!0;var i=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(R){var o=R}Reflect.construct(e,[],t)}else{try{t.call()}catch(R){o=R}e.call(t.prototype)}else{try{throw Error()}catch(R){o=R}e()}}catch(R){if(R&&o&&typeof R.stack=="string"){for(var l=R.stack.split(`
`),c=o.stack.split(`
`),m=l.length-1,E=c.length-1;1<=m&&0<=E&&l[m]!==c[E];)E--;for(;1<=m&&0<=E;m--,E--)if(l[m]!==c[E]){if(m!==1||E!==1)do if(m--,E--,0>E||l[m]!==c[E]){var T=`
`+l[m].replace(" at new "," at ");return e.displayName&&T.includes("<anonymous>")&&(T=T.replace("<anonymous>",e.displayName)),T}while(1<=m&&0<=E);break}}}finally{ie=!1,Error.prepareStackTrace=i}return(e=e?e.displayName||e.name:"")?_(e):""}function ce(e){switch(e.tag){case 5:return _(e.type);case 16:return _("Lazy");case 13:return _("Suspense");case 19:return _("SuspenseList");case 0:case 2:case 15:return e=ae(e.type,!1),e;case 11:return e=ae(e.type.render,!1),e;case 1:return e=ae(e.type,!0),e;default:return""}}function de(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case re:return"Fragment";case X:return"Portal";case me:return"Profiler";case Z:return"StrictMode";case Qe:return"Suspense";case Je:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case qe:return(e.displayName||"Context")+".Consumer";case we:return(e._context.displayName||"Context")+".Provider";case ut:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Tt:return t=e.displayName||null,t!==null?t:de(e.type)||"Memo";case Ge:t=e._payload,e=e._init;try{return de(e(t))}catch{}}return null}function ge(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return de(t);case 8:return t===Z?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function he(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Se(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function ct(e){var t=Se(e)?"checked":"value",i=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),o=""+e[t];if(!e.hasOwnProperty(t)&&typeof i<"u"&&typeof i.get=="function"&&typeof i.set=="function"){var l=i.get,c=i.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(m){o=""+m,c.call(this,m)}}),Object.defineProperty(e,t,{enumerable:i.enumerable}),{getValue:function(){return o},setValue:function(m){o=""+m},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function _i(e){e._valueTracker||(e._valueTracker=ct(e))}function Bu(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var i=t.getValue(),o="";return e&&(o=Se(e)?e.checked?"true":"false":e.value),e=o,e!==i?(t.setValue(e),!0):!1}function bi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Eo(e,t){var i=t.checked;return W({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:i??e._wrapperState.initialChecked})}function zu(e,t){var i=t.defaultValue==null?"":t.defaultValue,o=t.checked!=null?t.checked:t.defaultChecked;i=he(t.value!=null?t.value:i),e._wrapperState={initialChecked:o,initialValue:i,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Uu(e,t){t=t.checked,t!=null&&F(e,"checked",t,!1)}function Co(e,t){Uu(e,t);var i=he(t.value),o=t.type;if(i!=null)o==="number"?(i===0&&e.value===""||e.value!=i)&&(e.value=""+i):e.value!==""+i&&(e.value=""+i);else if(o==="submit"||o==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?To(e,t.type,i):t.hasOwnProperty("defaultValue")&&To(e,t.type,he(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function $u(e,t,i){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var o=t.type;if(!(o!=="submit"&&o!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,i||t===e.value||(e.value=t),e.defaultValue=t}i=e.name,i!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,i!==""&&(e.name=i)}function To(e,t,i){(t!=="number"||bi(e.ownerDocument)!==e)&&(i==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+i&&(e.defaultValue=""+i))}var Rr=Array.isArray;function Qn(e,t,i,o){if(e=e.options,t){t={};for(var l=0;l<i.length;l++)t["$"+i[l]]=!0;for(i=0;i<e.length;i++)l=t.hasOwnProperty("$"+e[i].value),e[i].selected!==l&&(e[i].selected=l),l&&o&&(e[i].defaultSelected=!0)}else{for(i=""+he(i),t=null,l=0;l<e.length;l++){if(e[l].value===i){e[l].selected=!0,o&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function ko(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(s(91));return W({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Wu(e,t){var i=t.value;if(i==null){if(i=t.children,t=t.defaultValue,i!=null){if(t!=null)throw Error(s(92));if(Rr(i)){if(1<i.length)throw Error(s(93));i=i[0]}t=i}t==null&&(t=""),i=t}e._wrapperState={initialValue:he(i)}}function Hu(e,t){var i=he(t.value),o=he(t.defaultValue);i!=null&&(i=""+i,i!==e.value&&(e.value=i),t.defaultValue==null&&e.defaultValue!==i&&(e.defaultValue=i)),o!=null&&(e.defaultValue=""+o)}function Ku(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Qu(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Po(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Qu(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Oi,Gu=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,i,o,l){MSApp.execUnsafeLocalFunction(function(){return e(t,i,o,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Oi=Oi||document.createElement("div"),Oi.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Oi.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Mr(e,t){if(t){var i=e.firstChild;if(i&&i===e.lastChild&&i.nodeType===3){i.nodeValue=t;return}}e.textContent=t}var Ar={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},y0=["Webkit","ms","Moz","O"];Object.keys(Ar).forEach(function(e){y0.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Ar[t]=Ar[e]})});function Yu(e,t,i){return t==null||typeof t=="boolean"||t===""?"":i||typeof t!="number"||t===0||Ar.hasOwnProperty(e)&&Ar[e]?(""+t).trim():t+"px"}function Xu(e,t){e=e.style;for(var i in t)if(t.hasOwnProperty(i)){var o=i.indexOf("--")===0,l=Yu(i,t[i],o);i==="float"&&(i="cssFloat"),o?e.setProperty(i,l):e[i]=l}}var v0=W({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function jo(e,t){if(t){if(v0[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(s(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(s(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(s(61))}if(t.style!=null&&typeof t.style!="object")throw Error(s(62))}}function No(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ro=null;function Mo(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ao=null,Gn=null,Yn=null;function Zu(e){if(e=Jr(e)){if(typeof Ao!="function")throw Error(s(280));var t=e.stateNode;t&&(t=os(t),Ao(e.stateNode,e.type,t))}}function qu(e){Gn?Yn?Yn.push(e):Yn=[e]:Gn=e}function Ju(){if(Gn){var e=Gn,t=Yn;if(Yn=Gn=null,Zu(e),t)for(e=0;e<t.length;e++)Zu(t[e])}}function ec(e,t){return e(t)}function tc(){}var Lo=!1;function nc(e,t,i){if(Lo)return e(t,i);Lo=!0;try{return ec(e,t,i)}finally{Lo=!1,(Gn!==null||Yn!==null)&&(tc(),Ju())}}function Lr(e,t){var i=e.stateNode;if(i===null)return null;var o=os(i);if(o===null)return null;i=o[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(o=!o.disabled)||(e=e.type,o=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!o;break e;default:e=!1}if(e)return null;if(i&&typeof i!="function")throw Error(s(231,t,typeof i));return i}var Do=!1;if(h)try{var Dr={};Object.defineProperty(Dr,"passive",{get:function(){Do=!0}}),window.addEventListener("test",Dr,Dr),window.removeEventListener("test",Dr,Dr)}catch{Do=!1}function x0(e,t,i,o,l,c,m,E,T){var R=Array.prototype.slice.call(arguments,3);try{t.apply(i,R)}catch(b){this.onError(b)}}var Vr=!1,Fi=null,Ii=!1,Vo=null,w0={onError:function(e){Vr=!0,Fi=e}};function S0(e,t,i,o,l,c,m,E,T){Vr=!1,Fi=null,x0.apply(w0,arguments)}function E0(e,t,i,o,l,c,m,E,T){if(S0.apply(this,arguments),Vr){if(Vr){var R=Fi;Vr=!1,Fi=null}else throw Error(s(198));Ii||(Ii=!0,Vo=R)}}function Pn(e){var t=e,i=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(i=t.return),e=t.return;while(e)}return t.tag===3?i:null}function rc(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function ic(e){if(Pn(e)!==e)throw Error(s(188))}function C0(e){var t=e.alternate;if(!t){if(t=Pn(e),t===null)throw Error(s(188));return t!==e?null:e}for(var i=e,o=t;;){var l=i.return;if(l===null)break;var c=l.alternate;if(c===null){if(o=l.return,o!==null){i=o;continue}break}if(l.child===c.child){for(c=l.child;c;){if(c===i)return ic(l),e;if(c===o)return ic(l),t;c=c.sibling}throw Error(s(188))}if(i.return!==o.return)i=l,o=c;else{for(var m=!1,E=l.child;E;){if(E===i){m=!0,i=l,o=c;break}if(E===o){m=!0,o=l,i=c;break}E=E.sibling}if(!m){for(E=c.child;E;){if(E===i){m=!0,i=c,o=l;break}if(E===o){m=!0,o=c,i=l;break}E=E.sibling}if(!m)throw Error(s(189))}}if(i.alternate!==o)throw Error(s(190))}if(i.tag!==3)throw Error(s(188));return i.stateNode.current===i?e:t}function sc(e){return e=C0(e),e!==null?oc(e):null}function oc(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=oc(e);if(t!==null)return t;e=e.sibling}return null}var ac=r.unstable_scheduleCallback,lc=r.unstable_cancelCallback,T0=r.unstable_shouldYield,k0=r.unstable_requestPaint,Re=r.unstable_now,P0=r.unstable_getCurrentPriorityLevel,_o=r.unstable_ImmediatePriority,uc=r.unstable_UserBlockingPriority,Bi=r.unstable_NormalPriority,j0=r.unstable_LowPriority,cc=r.unstable_IdlePriority,zi=null,Dt=null;function N0(e){if(Dt&&typeof Dt.onCommitFiberRoot=="function")try{Dt.onCommitFiberRoot(zi,e,void 0,(e.current.flags&128)===128)}catch{}}var kt=Math.clz32?Math.clz32:A0,R0=Math.log,M0=Math.LN2;function A0(e){return e>>>=0,e===0?32:31-(R0(e)/M0|0)|0}var Ui=64,$i=4194304;function _r(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Wi(e,t){var i=e.pendingLanes;if(i===0)return 0;var o=0,l=e.suspendedLanes,c=e.pingedLanes,m=i&268435455;if(m!==0){var E=m&~l;E!==0?o=_r(E):(c&=m,c!==0&&(o=_r(c)))}else m=i&~l,m!==0?o=_r(m):c!==0&&(o=_r(c));if(o===0)return 0;if(t!==0&&t!==o&&(t&l)===0&&(l=o&-o,c=t&-t,l>=c||l===16&&(c&4194240)!==0))return t;if((o&4)!==0&&(o|=i&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=o;0<t;)i=31-kt(t),l=1<<i,o|=e[i],t&=~l;return o}function L0(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function D0(e,t){for(var i=e.suspendedLanes,o=e.pingedLanes,l=e.expirationTimes,c=e.pendingLanes;0<c;){var m=31-kt(c),E=1<<m,T=l[m];T===-1?((E&i)===0||(E&o)!==0)&&(l[m]=L0(E,t)):T<=t&&(e.expiredLanes|=E),c&=~E}}function bo(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function dc(){var e=Ui;return Ui<<=1,(Ui&4194240)===0&&(Ui=64),e}function Oo(e){for(var t=[],i=0;31>i;i++)t.push(e);return t}function br(e,t,i){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-kt(t),e[t]=i}function V0(e,t){var i=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var o=e.eventTimes;for(e=e.expirationTimes;0<i;){var l=31-kt(i),c=1<<l;t[l]=0,o[l]=-1,e[l]=-1,i&=~c}}function Fo(e,t){var i=e.entangledLanes|=t;for(e=e.entanglements;i;){var o=31-kt(i),l=1<<o;l&t|e[o]&t&&(e[o]|=t),i&=~l}}var pe=0;function fc(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var hc,Io,pc,mc,gc,Bo=!1,Hi=[],Jt=null,en=null,tn=null,Or=new Map,Fr=new Map,nn=[],_0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function yc(e,t){switch(e){case"focusin":case"focusout":Jt=null;break;case"dragenter":case"dragleave":en=null;break;case"mouseover":case"mouseout":tn=null;break;case"pointerover":case"pointerout":Or.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Fr.delete(t.pointerId)}}function Ir(e,t,i,o,l,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:i,eventSystemFlags:o,nativeEvent:c,targetContainers:[l]},t!==null&&(t=Jr(t),t!==null&&Io(t)),e):(e.eventSystemFlags|=o,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function b0(e,t,i,o,l){switch(t){case"focusin":return Jt=Ir(Jt,e,t,i,o,l),!0;case"dragenter":return en=Ir(en,e,t,i,o,l),!0;case"mouseover":return tn=Ir(tn,e,t,i,o,l),!0;case"pointerover":var c=l.pointerId;return Or.set(c,Ir(Or.get(c)||null,e,t,i,o,l)),!0;case"gotpointercapture":return c=l.pointerId,Fr.set(c,Ir(Fr.get(c)||null,e,t,i,o,l)),!0}return!1}function vc(e){var t=jn(e.target);if(t!==null){var i=Pn(t);if(i!==null){if(t=i.tag,t===13){if(t=rc(i),t!==null){e.blockedOn=t,gc(e.priority,function(){pc(i)});return}}else if(t===3&&i.stateNode.current.memoizedState.isDehydrated){e.blockedOn=i.tag===3?i.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ki(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var i=Uo(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(i===null){i=e.nativeEvent;var o=new i.constructor(i.type,i);Ro=o,i.target.dispatchEvent(o),Ro=null}else return t=Jr(i),t!==null&&Io(t),e.blockedOn=i,!1;t.shift()}return!0}function xc(e,t,i){Ki(e)&&i.delete(t)}function O0(){Bo=!1,Jt!==null&&Ki(Jt)&&(Jt=null),en!==null&&Ki(en)&&(en=null),tn!==null&&Ki(tn)&&(tn=null),Or.forEach(xc),Fr.forEach(xc)}function Br(e,t){e.blockedOn===t&&(e.blockedOn=null,Bo||(Bo=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,O0)))}function zr(e){function t(l){return Br(l,e)}if(0<Hi.length){Br(Hi[0],e);for(var i=1;i<Hi.length;i++){var o=Hi[i];o.blockedOn===e&&(o.blockedOn=null)}}for(Jt!==null&&Br(Jt,e),en!==null&&Br(en,e),tn!==null&&Br(tn,e),Or.forEach(t),Fr.forEach(t),i=0;i<nn.length;i++)o=nn[i],o.blockedOn===e&&(o.blockedOn=null);for(;0<nn.length&&(i=nn[0],i.blockedOn===null);)vc(i),i.blockedOn===null&&nn.shift()}var Xn=Y.ReactCurrentBatchConfig,Qi=!0;function F0(e,t,i,o){var l=pe,c=Xn.transition;Xn.transition=null;try{pe=1,zo(e,t,i,o)}finally{pe=l,Xn.transition=c}}function I0(e,t,i,o){var l=pe,c=Xn.transition;Xn.transition=null;try{pe=4,zo(e,t,i,o)}finally{pe=l,Xn.transition=c}}function zo(e,t,i,o){if(Qi){var l=Uo(e,t,i,o);if(l===null)sa(e,t,o,Gi,i),yc(e,o);else if(b0(l,e,t,i,o))o.stopPropagation();else if(yc(e,o),t&4&&-1<_0.indexOf(e)){for(;l!==null;){var c=Jr(l);if(c!==null&&hc(c),c=Uo(e,t,i,o),c===null&&sa(e,t,o,Gi,i),c===l)break;l=c}l!==null&&o.stopPropagation()}else sa(e,t,o,null,i)}}var Gi=null;function Uo(e,t,i,o){if(Gi=null,e=Mo(o),e=jn(e),e!==null)if(t=Pn(e),t===null)e=null;else if(i=t.tag,i===13){if(e=rc(t),e!==null)return e;e=null}else if(i===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Gi=e,null}function wc(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(P0()){case _o:return 1;case uc:return 4;case Bi:case j0:return 16;case cc:return 536870912;default:return 16}default:return 16}}var rn=null,$o=null,Yi=null;function Sc(){if(Yi)return Yi;var e,t=$o,i=t.length,o,l="value"in rn?rn.value:rn.textContent,c=l.length;for(e=0;e<i&&t[e]===l[e];e++);var m=i-e;for(o=1;o<=m&&t[i-o]===l[c-o];o++);return Yi=l.slice(e,1<o?1-o:void 0)}function Xi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Zi(){return!0}function Ec(){return!1}function dt(e){function t(i,o,l,c,m){this._reactName=i,this._targetInst=l,this.type=o,this.nativeEvent=c,this.target=m,this.currentTarget=null;for(var E in e)e.hasOwnProperty(E)&&(i=e[E],this[E]=i?i(c):c[E]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?Zi:Ec,this.isPropagationStopped=Ec,this}return W(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var i=this.nativeEvent;i&&(i.preventDefault?i.preventDefault():typeof i.returnValue!="unknown"&&(i.returnValue=!1),this.isDefaultPrevented=Zi)},stopPropagation:function(){var i=this.nativeEvent;i&&(i.stopPropagation?i.stopPropagation():typeof i.cancelBubble!="unknown"&&(i.cancelBubble=!0),this.isPropagationStopped=Zi)},persist:function(){},isPersistent:Zi}),t}var Zn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Wo=dt(Zn),Ur=W({},Zn,{view:0,detail:0}),B0=dt(Ur),Ho,Ko,$r,qi=W({},Ur,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Go,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==$r&&($r&&e.type==="mousemove"?(Ho=e.screenX-$r.screenX,Ko=e.screenY-$r.screenY):Ko=Ho=0,$r=e),Ho)},movementY:function(e){return"movementY"in e?e.movementY:Ko}}),Cc=dt(qi),z0=W({},qi,{dataTransfer:0}),U0=dt(z0),$0=W({},Ur,{relatedTarget:0}),Qo=dt($0),W0=W({},Zn,{animationName:0,elapsedTime:0,pseudoElement:0}),H0=dt(W0),K0=W({},Zn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Q0=dt(K0),G0=W({},Zn,{data:0}),Tc=dt(G0),Y0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},X0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Z0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function q0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Z0[e])?!!t[e]:!1}function Go(){return q0}var J0=W({},Ur,{key:function(e){if(e.key){var t=Y0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Xi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?X0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Go,charCode:function(e){return e.type==="keypress"?Xi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Xi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),eg=dt(J0),tg=W({},qi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),kc=dt(tg),ng=W({},Ur,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Go}),rg=dt(ng),ig=W({},Zn,{propertyName:0,elapsedTime:0,pseudoElement:0}),sg=dt(ig),og=W({},qi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ag=dt(og),lg=[9,13,27,32],Yo=h&&"CompositionEvent"in window,Wr=null;h&&"documentMode"in document&&(Wr=document.documentMode);var ug=h&&"TextEvent"in window&&!Wr,Pc=h&&(!Yo||Wr&&8<Wr&&11>=Wr),jc=" ",Nc=!1;function Rc(e,t){switch(e){case"keyup":return lg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Mc(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var qn=!1;function cg(e,t){switch(e){case"compositionend":return Mc(t);case"keypress":return t.which!==32?null:(Nc=!0,jc);case"textInput":return e=t.data,e===jc&&Nc?null:e;default:return null}}function dg(e,t){if(qn)return e==="compositionend"||!Yo&&Rc(e,t)?(e=Sc(),Yi=$o=rn=null,qn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Pc&&t.locale!=="ko"?null:t.data;default:return null}}var fg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ac(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!fg[e.type]:t==="textarea"}function Lc(e,t,i,o){qu(o),t=rs(t,"onChange"),0<t.length&&(i=new Wo("onChange","change",null,i,o),e.push({event:i,listeners:t}))}var Hr=null,Kr=null;function hg(e){Xc(e,0)}function Ji(e){var t=rr(e);if(Bu(t))return e}function pg(e,t){if(e==="change")return t}var Dc=!1;if(h){var Xo;if(h){var Zo="oninput"in document;if(!Zo){var Vc=document.createElement("div");Vc.setAttribute("oninput","return;"),Zo=typeof Vc.oninput=="function"}Xo=Zo}else Xo=!1;Dc=Xo&&(!document.documentMode||9<document.documentMode)}function _c(){Hr&&(Hr.detachEvent("onpropertychange",bc),Kr=Hr=null)}function bc(e){if(e.propertyName==="value"&&Ji(Kr)){var t=[];Lc(t,Kr,e,Mo(e)),nc(hg,t)}}function mg(e,t,i){e==="focusin"?(_c(),Hr=t,Kr=i,Hr.attachEvent("onpropertychange",bc)):e==="focusout"&&_c()}function gg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ji(Kr)}function yg(e,t){if(e==="click")return Ji(t)}function vg(e,t){if(e==="input"||e==="change")return Ji(t)}function xg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Pt=typeof Object.is=="function"?Object.is:xg;function Qr(e,t){if(Pt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var i=Object.keys(e),o=Object.keys(t);if(i.length!==o.length)return!1;for(o=0;o<i.length;o++){var l=i[o];if(!p.call(t,l)||!Pt(e[l],t[l]))return!1}return!0}function Oc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Fc(e,t){var i=Oc(e);e=0;for(var o;i;){if(i.nodeType===3){if(o=e+i.textContent.length,e<=t&&o>=t)return{node:i,offset:t-e};e=o}e:{for(;i;){if(i.nextSibling){i=i.nextSibling;break e}i=i.parentNode}i=void 0}i=Oc(i)}}function Ic(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Ic(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Bc(){for(var e=window,t=bi();t instanceof e.HTMLIFrameElement;){try{var i=typeof t.contentWindow.location.href=="string"}catch{i=!1}if(i)e=t.contentWindow;else break;t=bi(e.document)}return t}function qo(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function wg(e){var t=Bc(),i=e.focusedElem,o=e.selectionRange;if(t!==i&&i&&i.ownerDocument&&Ic(i.ownerDocument.documentElement,i)){if(o!==null&&qo(i)){if(t=o.start,e=o.end,e===void 0&&(e=t),"selectionStart"in i)i.selectionStart=t,i.selectionEnd=Math.min(e,i.value.length);else if(e=(t=i.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=i.textContent.length,c=Math.min(o.start,l);o=o.end===void 0?c:Math.min(o.end,l),!e.extend&&c>o&&(l=o,o=c,c=l),l=Fc(i,c);var m=Fc(i,o);l&&m&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==m.node||e.focusOffset!==m.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),c>o?(e.addRange(t),e.extend(m.node,m.offset)):(t.setEnd(m.node,m.offset),e.addRange(t)))}}for(t=[],e=i;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof i.focus=="function"&&i.focus(),i=0;i<t.length;i++)e=t[i],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Sg=h&&"documentMode"in document&&11>=document.documentMode,Jn=null,Jo=null,Gr=null,ea=!1;function zc(e,t,i){var o=i.window===i?i.document:i.nodeType===9?i:i.ownerDocument;ea||Jn==null||Jn!==bi(o)||(o=Jn,"selectionStart"in o&&qo(o)?o={start:o.selectionStart,end:o.selectionEnd}:(o=(o.ownerDocument&&o.ownerDocument.defaultView||window).getSelection(),o={anchorNode:o.anchorNode,anchorOffset:o.anchorOffset,focusNode:o.focusNode,focusOffset:o.focusOffset}),Gr&&Qr(Gr,o)||(Gr=o,o=rs(Jo,"onSelect"),0<o.length&&(t=new Wo("onSelect","select",null,t,i),e.push({event:t,listeners:o}),t.target=Jn)))}function es(e,t){var i={};return i[e.toLowerCase()]=t.toLowerCase(),i["Webkit"+e]="webkit"+t,i["Moz"+e]="moz"+t,i}var er={animationend:es("Animation","AnimationEnd"),animationiteration:es("Animation","AnimationIteration"),animationstart:es("Animation","AnimationStart"),transitionend:es("Transition","TransitionEnd")},ta={},Uc={};h&&(Uc=document.createElement("div").style,"AnimationEvent"in window||(delete er.animationend.animation,delete er.animationiteration.animation,delete er.animationstart.animation),"TransitionEvent"in window||delete er.transitionend.transition);function ts(e){if(ta[e])return ta[e];if(!er[e])return e;var t=er[e],i;for(i in t)if(t.hasOwnProperty(i)&&i in Uc)return ta[e]=t[i];return e}var $c=ts("animationend"),Wc=ts("animationiteration"),Hc=ts("animationstart"),Kc=ts("transitionend"),Qc=new Map,Gc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function sn(e,t){Qc.set(e,t),d(t,[e])}for(var na=0;na<Gc.length;na++){var ra=Gc[na],Eg=ra.toLowerCase(),Cg=ra[0].toUpperCase()+ra.slice(1);sn(Eg,"on"+Cg)}sn($c,"onAnimationEnd"),sn(Wc,"onAnimationIteration"),sn(Hc,"onAnimationStart"),sn("dblclick","onDoubleClick"),sn("focusin","onFocus"),sn("focusout","onBlur"),sn(Kc,"onTransitionEnd"),f("onMouseEnter",["mouseout","mouseover"]),f("onMouseLeave",["mouseout","mouseover"]),f("onPointerEnter",["pointerout","pointerover"]),f("onPointerLeave",["pointerout","pointerover"]),d("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),d("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),d("onBeforeInput",["compositionend","keypress","textInput","paste"]),d("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),d("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),d("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Yr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Tg=new Set("cancel close invalid load scroll toggle".split(" ").concat(Yr));function Yc(e,t,i){var o=e.type||"unknown-event";e.currentTarget=i,E0(o,t,void 0,e),e.currentTarget=null}function Xc(e,t){t=(t&4)!==0;for(var i=0;i<e.length;i++){var o=e[i],l=o.event;o=o.listeners;e:{var c=void 0;if(t)for(var m=o.length-1;0<=m;m--){var E=o[m],T=E.instance,R=E.currentTarget;if(E=E.listener,T!==c&&l.isPropagationStopped())break e;Yc(l,E,R),c=T}else for(m=0;m<o.length;m++){if(E=o[m],T=E.instance,R=E.currentTarget,E=E.listener,T!==c&&l.isPropagationStopped())break e;Yc(l,E,R),c=T}}}if(Ii)throw e=Vo,Ii=!1,Vo=null,e}function ve(e,t){var i=t[da];i===void 0&&(i=t[da]=new Set);var o=e+"__bubble";i.has(o)||(Zc(t,e,2,!1),i.add(o))}function ia(e,t,i){var o=0;t&&(o|=4),Zc(i,e,o,t)}var ns="_reactListening"+Math.random().toString(36).slice(2);function Xr(e){if(!e[ns]){e[ns]=!0,a.forEach(function(i){i!=="selectionchange"&&(Tg.has(i)||ia(i,!1,e),ia(i,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ns]||(t[ns]=!0,ia("selectionchange",!1,t))}}function Zc(e,t,i,o){switch(wc(t)){case 1:var l=F0;break;case 4:l=I0;break;default:l=zo}i=l.bind(null,t,i,e),l=void 0,!Do||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),o?l!==void 0?e.addEventListener(t,i,{capture:!0,passive:l}):e.addEventListener(t,i,!0):l!==void 0?e.addEventListener(t,i,{passive:l}):e.addEventListener(t,i,!1)}function sa(e,t,i,o,l){var c=o;if((t&1)===0&&(t&2)===0&&o!==null)e:for(;;){if(o===null)return;var m=o.tag;if(m===3||m===4){var E=o.stateNode.containerInfo;if(E===l||E.nodeType===8&&E.parentNode===l)break;if(m===4)for(m=o.return;m!==null;){var T=m.tag;if((T===3||T===4)&&(T=m.stateNode.containerInfo,T===l||T.nodeType===8&&T.parentNode===l))return;m=m.return}for(;E!==null;){if(m=jn(E),m===null)return;if(T=m.tag,T===5||T===6){o=c=m;continue e}E=E.parentNode}}o=o.return}nc(function(){var R=c,b=Mo(i),O=[];e:{var V=Qc.get(e);if(V!==void 0){var $=Wo,K=e;switch(e){case"keypress":if(Xi(i)===0)break e;case"keydown":case"keyup":$=eg;break;case"focusin":K="focus",$=Qo;break;case"focusout":K="blur",$=Qo;break;case"beforeblur":case"afterblur":$=Qo;break;case"click":if(i.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":$=Cc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":$=U0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":$=rg;break;case $c:case Wc:case Hc:$=H0;break;case Kc:$=sg;break;case"scroll":$=B0;break;case"wheel":$=ag;break;case"copy":case"cut":case"paste":$=Q0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":$=kc}var Q=(t&4)!==0,Me=!Q&&e==="scroll",j=Q?V!==null?V+"Capture":null:V;Q=[];for(var k=R,N;k!==null;){N=k;var I=N.stateNode;if(N.tag===5&&I!==null&&(N=I,j!==null&&(I=Lr(k,j),I!=null&&Q.push(Zr(k,I,N)))),Me)break;k=k.return}0<Q.length&&(V=new $(V,K,null,i,b),O.push({event:V,listeners:Q}))}}if((t&7)===0){e:{if(V=e==="mouseover"||e==="pointerover",$=e==="mouseout"||e==="pointerout",V&&i!==Ro&&(K=i.relatedTarget||i.fromElement)&&(jn(K)||K[zt]))break e;if(($||V)&&(V=b.window===b?b:(V=b.ownerDocument)?V.defaultView||V.parentWindow:window,$?(K=i.relatedTarget||i.toElement,$=R,K=K?jn(K):null,K!==null&&(Me=Pn(K),K!==Me||K.tag!==5&&K.tag!==6)&&(K=null)):($=null,K=R),$!==K)){if(Q=Cc,I="onMouseLeave",j="onMouseEnter",k="mouse",(e==="pointerout"||e==="pointerover")&&(Q=kc,I="onPointerLeave",j="onPointerEnter",k="pointer"),Me=$==null?V:rr($),N=K==null?V:rr(K),V=new Q(I,k+"leave",$,i,b),V.target=Me,V.relatedTarget=N,I=null,jn(b)===R&&(Q=new Q(j,k+"enter",K,i,b),Q.target=N,Q.relatedTarget=Me,I=Q),Me=I,$&&K)t:{for(Q=$,j=K,k=0,N=Q;N;N=tr(N))k++;for(N=0,I=j;I;I=tr(I))N++;for(;0<k-N;)Q=tr(Q),k--;for(;0<N-k;)j=tr(j),N--;for(;k--;){if(Q===j||j!==null&&Q===j.alternate)break t;Q=tr(Q),j=tr(j)}Q=null}else Q=null;$!==null&&qc(O,V,$,Q,!1),K!==null&&Me!==null&&qc(O,Me,K,Q,!0)}}e:{if(V=R?rr(R):window,$=V.nodeName&&V.nodeName.toLowerCase(),$==="select"||$==="input"&&V.type==="file")var G=pg;else if(Ac(V))if(Dc)G=vg;else{G=gg;var J=mg}else($=V.nodeName)&&$.toLowerCase()==="input"&&(V.type==="checkbox"||V.type==="radio")&&(G=yg);if(G&&(G=G(e,R))){Lc(O,G,i,b);break e}J&&J(e,V,R),e==="focusout"&&(J=V._wrapperState)&&J.controlled&&V.type==="number"&&To(V,"number",V.value)}switch(J=R?rr(R):window,e){case"focusin":(Ac(J)||J.contentEditable==="true")&&(Jn=J,Jo=R,Gr=null);break;case"focusout":Gr=Jo=Jn=null;break;case"mousedown":ea=!0;break;case"contextmenu":case"mouseup":case"dragend":ea=!1,zc(O,i,b);break;case"selectionchange":if(Sg)break;case"keydown":case"keyup":zc(O,i,b)}var ee;if(Yo)e:{switch(e){case"compositionstart":var ne="onCompositionStart";break e;case"compositionend":ne="onCompositionEnd";break e;case"compositionupdate":ne="onCompositionUpdate";break e}ne=void 0}else qn?Rc(e,i)&&(ne="onCompositionEnd"):e==="keydown"&&i.keyCode===229&&(ne="onCompositionStart");ne&&(Pc&&i.locale!=="ko"&&(qn||ne!=="onCompositionStart"?ne==="onCompositionEnd"&&qn&&(ee=Sc()):(rn=b,$o="value"in rn?rn.value:rn.textContent,qn=!0)),J=rs(R,ne),0<J.length&&(ne=new Tc(ne,e,null,i,b),O.push({event:ne,listeners:J}),ee?ne.data=ee:(ee=Mc(i),ee!==null&&(ne.data=ee)))),(ee=ug?cg(e,i):dg(e,i))&&(R=rs(R,"onBeforeInput"),0<R.length&&(b=new Tc("onBeforeInput","beforeinput",null,i,b),O.push({event:b,listeners:R}),b.data=ee))}Xc(O,t)})}function Zr(e,t,i){return{instance:e,listener:t,currentTarget:i}}function rs(e,t){for(var i=t+"Capture",o=[];e!==null;){var l=e,c=l.stateNode;l.tag===5&&c!==null&&(l=c,c=Lr(e,i),c!=null&&o.unshift(Zr(e,c,l)),c=Lr(e,t),c!=null&&o.push(Zr(e,c,l))),e=e.return}return o}function tr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function qc(e,t,i,o,l){for(var c=t._reactName,m=[];i!==null&&i!==o;){var E=i,T=E.alternate,R=E.stateNode;if(T!==null&&T===o)break;E.tag===5&&R!==null&&(E=R,l?(T=Lr(i,c),T!=null&&m.unshift(Zr(i,T,E))):l||(T=Lr(i,c),T!=null&&m.push(Zr(i,T,E)))),i=i.return}m.length!==0&&e.push({event:t,listeners:m})}var kg=/\r\n?/g,Pg=/\u0000|\uFFFD/g;function Jc(e){return(typeof e=="string"?e:""+e).replace(kg,`
`).replace(Pg,"")}function is(e,t,i){if(t=Jc(t),Jc(e)!==t&&i)throw Error(s(425))}function ss(){}var oa=null,aa=null;function la(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var ua=typeof setTimeout=="function"?setTimeout:void 0,jg=typeof clearTimeout=="function"?clearTimeout:void 0,ed=typeof Promise=="function"?Promise:void 0,Ng=typeof queueMicrotask=="function"?queueMicrotask:typeof ed<"u"?function(e){return ed.resolve(null).then(e).catch(Rg)}:ua;function Rg(e){setTimeout(function(){throw e})}function ca(e,t){var i=t,o=0;do{var l=i.nextSibling;if(e.removeChild(i),l&&l.nodeType===8)if(i=l.data,i==="/$"){if(o===0){e.removeChild(l),zr(t);return}o--}else i!=="$"&&i!=="$?"&&i!=="$!"||o++;i=l}while(i);zr(t)}function on(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function td(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var i=e.data;if(i==="$"||i==="$!"||i==="$?"){if(t===0)return e;t--}else i==="/$"&&t++}e=e.previousSibling}return null}var nr=Math.random().toString(36).slice(2),Vt="__reactFiber$"+nr,qr="__reactProps$"+nr,zt="__reactContainer$"+nr,da="__reactEvents$"+nr,Mg="__reactListeners$"+nr,Ag="__reactHandles$"+nr;function jn(e){var t=e[Vt];if(t)return t;for(var i=e.parentNode;i;){if(t=i[zt]||i[Vt]){if(i=t.alternate,t.child!==null||i!==null&&i.child!==null)for(e=td(e);e!==null;){if(i=e[Vt])return i;e=td(e)}return t}e=i,i=e.parentNode}return null}function Jr(e){return e=e[Vt]||e[zt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function rr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(s(33))}function os(e){return e[qr]||null}var fa=[],ir=-1;function an(e){return{current:e}}function xe(e){0>ir||(e.current=fa[ir],fa[ir]=null,ir--)}function ye(e,t){ir++,fa[ir]=e.current,e.current=t}var ln={},Ue=an(ln),et=an(!1),Nn=ln;function sr(e,t){var i=e.type.contextTypes;if(!i)return ln;var o=e.stateNode;if(o&&o.__reactInternalMemoizedUnmaskedChildContext===t)return o.__reactInternalMemoizedMaskedChildContext;var l={},c;for(c in i)l[c]=t[c];return o&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function tt(e){return e=e.childContextTypes,e!=null}function as(){xe(et),xe(Ue)}function nd(e,t,i){if(Ue.current!==ln)throw Error(s(168));ye(Ue,t),ye(et,i)}function rd(e,t,i){var o=e.stateNode;if(t=t.childContextTypes,typeof o.getChildContext!="function")return i;o=o.getChildContext();for(var l in o)if(!(l in t))throw Error(s(108,ge(e)||"Unknown",l));return W({},i,o)}function ls(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||ln,Nn=Ue.current,ye(Ue,e),ye(et,et.current),!0}function id(e,t,i){var o=e.stateNode;if(!o)throw Error(s(169));i?(e=rd(e,t,Nn),o.__reactInternalMemoizedMergedChildContext=e,xe(et),xe(Ue),ye(Ue,e)):xe(et),ye(et,i)}var Ut=null,us=!1,ha=!1;function sd(e){Ut===null?Ut=[e]:Ut.push(e)}function Lg(e){us=!0,sd(e)}function un(){if(!ha&&Ut!==null){ha=!0;var e=0,t=pe;try{var i=Ut;for(pe=1;e<i.length;e++){var o=i[e];do o=o(!0);while(o!==null)}Ut=null,us=!1}catch(l){throw Ut!==null&&(Ut=Ut.slice(e+1)),ac(_o,un),l}finally{pe=t,ha=!1}}return null}var or=[],ar=0,cs=null,ds=0,mt=[],gt=0,Rn=null,$t=1,Wt="";function Mn(e,t){or[ar++]=ds,or[ar++]=cs,cs=e,ds=t}function od(e,t,i){mt[gt++]=$t,mt[gt++]=Wt,mt[gt++]=Rn,Rn=e;var o=$t;e=Wt;var l=32-kt(o)-1;o&=~(1<<l),i+=1;var c=32-kt(t)+l;if(30<c){var m=l-l%5;c=(o&(1<<m)-1).toString(32),o>>=m,l-=m,$t=1<<32-kt(t)+l|i<<l|o,Wt=c+e}else $t=1<<c|i<<l|o,Wt=e}function pa(e){e.return!==null&&(Mn(e,1),od(e,1,0))}function ma(e){for(;e===cs;)cs=or[--ar],or[ar]=null,ds=or[--ar],or[ar]=null;for(;e===Rn;)Rn=mt[--gt],mt[gt]=null,Wt=mt[--gt],mt[gt]=null,$t=mt[--gt],mt[gt]=null}var ft=null,ht=null,Ee=!1,jt=null;function ad(e,t){var i=wt(5,null,null,0);i.elementType="DELETED",i.stateNode=t,i.return=e,t=e.deletions,t===null?(e.deletions=[i],e.flags|=16):t.push(i)}function ld(e,t){switch(e.tag){case 5:var i=e.type;return t=t.nodeType!==1||i.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,ft=e,ht=on(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,ft=e,ht=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(i=Rn!==null?{id:$t,overflow:Wt}:null,e.memoizedState={dehydrated:t,treeContext:i,retryLane:1073741824},i=wt(18,null,null,0),i.stateNode=t,i.return=e,e.child=i,ft=e,ht=null,!0):!1;default:return!1}}function ga(e){return(e.mode&1)!==0&&(e.flags&128)===0}function ya(e){if(Ee){var t=ht;if(t){var i=t;if(!ld(e,t)){if(ga(e))throw Error(s(418));t=on(i.nextSibling);var o=ft;t&&ld(e,t)?ad(o,i):(e.flags=e.flags&-4097|2,Ee=!1,ft=e)}}else{if(ga(e))throw Error(s(418));e.flags=e.flags&-4097|2,Ee=!1,ft=e}}}function ud(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;ft=e}function fs(e){if(e!==ft)return!1;if(!Ee)return ud(e),Ee=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!la(e.type,e.memoizedProps)),t&&(t=ht)){if(ga(e))throw cd(),Error(s(418));for(;t;)ad(e,t),t=on(t.nextSibling)}if(ud(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(s(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var i=e.data;if(i==="/$"){if(t===0){ht=on(e.nextSibling);break e}t--}else i!=="$"&&i!=="$!"&&i!=="$?"||t++}e=e.nextSibling}ht=null}}else ht=ft?on(e.stateNode.nextSibling):null;return!0}function cd(){for(var e=ht;e;)e=on(e.nextSibling)}function lr(){ht=ft=null,Ee=!1}function va(e){jt===null?jt=[e]:jt.push(e)}var Dg=Y.ReactCurrentBatchConfig;function ei(e,t,i){if(e=i.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(i._owner){if(i=i._owner,i){if(i.tag!==1)throw Error(s(309));var o=i.stateNode}if(!o)throw Error(s(147,e));var l=o,c=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===c?t.ref:(t=function(m){var E=l.refs;m===null?delete E[c]:E[c]=m},t._stringRef=c,t)}if(typeof e!="string")throw Error(s(284));if(!i._owner)throw Error(s(290,e))}return e}function hs(e,t){throw e=Object.prototype.toString.call(t),Error(s(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function dd(e){var t=e._init;return t(e._payload)}function fd(e){function t(j,k){if(e){var N=j.deletions;N===null?(j.deletions=[k],j.flags|=16):N.push(k)}}function i(j,k){if(!e)return null;for(;k!==null;)t(j,k),k=k.sibling;return null}function o(j,k){for(j=new Map;k!==null;)k.key!==null?j.set(k.key,k):j.set(k.index,k),k=k.sibling;return j}function l(j,k){return j=yn(j,k),j.index=0,j.sibling=null,j}function c(j,k,N){return j.index=N,e?(N=j.alternate,N!==null?(N=N.index,N<k?(j.flags|=2,k):N):(j.flags|=2,k)):(j.flags|=1048576,k)}function m(j){return e&&j.alternate===null&&(j.flags|=2),j}function E(j,k,N,I){return k===null||k.tag!==6?(k=ul(N,j.mode,I),k.return=j,k):(k=l(k,N),k.return=j,k)}function T(j,k,N,I){var G=N.type;return G===re?b(j,k,N.props.children,I,N.key):k!==null&&(k.elementType===G||typeof G=="object"&&G!==null&&G.$$typeof===Ge&&dd(G)===k.type)?(I=l(k,N.props),I.ref=ei(j,k,N),I.return=j,I):(I=Os(N.type,N.key,N.props,null,j.mode,I),I.ref=ei(j,k,N),I.return=j,I)}function R(j,k,N,I){return k===null||k.tag!==4||k.stateNode.containerInfo!==N.containerInfo||k.stateNode.implementation!==N.implementation?(k=cl(N,j.mode,I),k.return=j,k):(k=l(k,N.children||[]),k.return=j,k)}function b(j,k,N,I,G){return k===null||k.tag!==7?(k=Fn(N,j.mode,I,G),k.return=j,k):(k=l(k,N),k.return=j,k)}function O(j,k,N){if(typeof k=="string"&&k!==""||typeof k=="number")return k=ul(""+k,j.mode,N),k.return=j,k;if(typeof k=="object"&&k!==null){switch(k.$$typeof){case U:return N=Os(k.type,k.key,k.props,null,j.mode,N),N.ref=ei(j,null,k),N.return=j,N;case X:return k=cl(k,j.mode,N),k.return=j,k;case Ge:var I=k._init;return O(j,I(k._payload),N)}if(Rr(k)||q(k))return k=Fn(k,j.mode,N,null),k.return=j,k;hs(j,k)}return null}function V(j,k,N,I){var G=k!==null?k.key:null;if(typeof N=="string"&&N!==""||typeof N=="number")return G!==null?null:E(j,k,""+N,I);if(typeof N=="object"&&N!==null){switch(N.$$typeof){case U:return N.key===G?T(j,k,N,I):null;case X:return N.key===G?R(j,k,N,I):null;case Ge:return G=N._init,V(j,k,G(N._payload),I)}if(Rr(N)||q(N))return G!==null?null:b(j,k,N,I,null);hs(j,N)}return null}function $(j,k,N,I,G){if(typeof I=="string"&&I!==""||typeof I=="number")return j=j.get(N)||null,E(k,j,""+I,G);if(typeof I=="object"&&I!==null){switch(I.$$typeof){case U:return j=j.get(I.key===null?N:I.key)||null,T(k,j,I,G);case X:return j=j.get(I.key===null?N:I.key)||null,R(k,j,I,G);case Ge:var J=I._init;return $(j,k,N,J(I._payload),G)}if(Rr(I)||q(I))return j=j.get(N)||null,b(k,j,I,G,null);hs(k,I)}return null}function K(j,k,N,I){for(var G=null,J=null,ee=k,ne=k=0,Fe=null;ee!==null&&ne<N.length;ne++){ee.index>ne?(Fe=ee,ee=null):Fe=ee.sibling;var fe=V(j,ee,N[ne],I);if(fe===null){ee===null&&(ee=Fe);break}e&&ee&&fe.alternate===null&&t(j,ee),k=c(fe,k,ne),J===null?G=fe:J.sibling=fe,J=fe,ee=Fe}if(ne===N.length)return i(j,ee),Ee&&Mn(j,ne),G;if(ee===null){for(;ne<N.length;ne++)ee=O(j,N[ne],I),ee!==null&&(k=c(ee,k,ne),J===null?G=ee:J.sibling=ee,J=ee);return Ee&&Mn(j,ne),G}for(ee=o(j,ee);ne<N.length;ne++)Fe=$(ee,j,ne,N[ne],I),Fe!==null&&(e&&Fe.alternate!==null&&ee.delete(Fe.key===null?ne:Fe.key),k=c(Fe,k,ne),J===null?G=Fe:J.sibling=Fe,J=Fe);return e&&ee.forEach(function(vn){return t(j,vn)}),Ee&&Mn(j,ne),G}function Q(j,k,N,I){var G=q(N);if(typeof G!="function")throw Error(s(150));if(N=G.call(N),N==null)throw Error(s(151));for(var J=G=null,ee=k,ne=k=0,Fe=null,fe=N.next();ee!==null&&!fe.done;ne++,fe=N.next()){ee.index>ne?(Fe=ee,ee=null):Fe=ee.sibling;var vn=V(j,ee,fe.value,I);if(vn===null){ee===null&&(ee=Fe);break}e&&ee&&vn.alternate===null&&t(j,ee),k=c(vn,k,ne),J===null?G=vn:J.sibling=vn,J=vn,ee=Fe}if(fe.done)return i(j,ee),Ee&&Mn(j,ne),G;if(ee===null){for(;!fe.done;ne++,fe=N.next())fe=O(j,fe.value,I),fe!==null&&(k=c(fe,k,ne),J===null?G=fe:J.sibling=fe,J=fe);return Ee&&Mn(j,ne),G}for(ee=o(j,ee);!fe.done;ne++,fe=N.next())fe=$(ee,j,ne,fe.value,I),fe!==null&&(e&&fe.alternate!==null&&ee.delete(fe.key===null?ne:fe.key),k=c(fe,k,ne),J===null?G=fe:J.sibling=fe,J=fe);return e&&ee.forEach(function(fy){return t(j,fy)}),Ee&&Mn(j,ne),G}function Me(j,k,N,I){if(typeof N=="object"&&N!==null&&N.type===re&&N.key===null&&(N=N.props.children),typeof N=="object"&&N!==null){switch(N.$$typeof){case U:e:{for(var G=N.key,J=k;J!==null;){if(J.key===G){if(G=N.type,G===re){if(J.tag===7){i(j,J.sibling),k=l(J,N.props.children),k.return=j,j=k;break e}}else if(J.elementType===G||typeof G=="object"&&G!==null&&G.$$typeof===Ge&&dd(G)===J.type){i(j,J.sibling),k=l(J,N.props),k.ref=ei(j,J,N),k.return=j,j=k;break e}i(j,J);break}else t(j,J);J=J.sibling}N.type===re?(k=Fn(N.props.children,j.mode,I,N.key),k.return=j,j=k):(I=Os(N.type,N.key,N.props,null,j.mode,I),I.ref=ei(j,k,N),I.return=j,j=I)}return m(j);case X:e:{for(J=N.key;k!==null;){if(k.key===J)if(k.tag===4&&k.stateNode.containerInfo===N.containerInfo&&k.stateNode.implementation===N.implementation){i(j,k.sibling),k=l(k,N.children||[]),k.return=j,j=k;break e}else{i(j,k);break}else t(j,k);k=k.sibling}k=cl(N,j.mode,I),k.return=j,j=k}return m(j);case Ge:return J=N._init,Me(j,k,J(N._payload),I)}if(Rr(N))return K(j,k,N,I);if(q(N))return Q(j,k,N,I);hs(j,N)}return typeof N=="string"&&N!==""||typeof N=="number"?(N=""+N,k!==null&&k.tag===6?(i(j,k.sibling),k=l(k,N),k.return=j,j=k):(i(j,k),k=ul(N,j.mode,I),k.return=j,j=k),m(j)):i(j,k)}return Me}var ur=fd(!0),hd=fd(!1),ps=an(null),ms=null,cr=null,xa=null;function wa(){xa=cr=ms=null}function Sa(e){var t=ps.current;xe(ps),e._currentValue=t}function Ea(e,t,i){for(;e!==null;){var o=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,o!==null&&(o.childLanes|=t)):o!==null&&(o.childLanes&t)!==t&&(o.childLanes|=t),e===i)break;e=e.return}}function dr(e,t){ms=e,xa=cr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(nt=!0),e.firstContext=null)}function yt(e){var t=e._currentValue;if(xa!==e)if(e={context:e,memoizedValue:t,next:null},cr===null){if(ms===null)throw Error(s(308));cr=e,ms.dependencies={lanes:0,firstContext:e}}else cr=cr.next=e;return t}var An=null;function Ca(e){An===null?An=[e]:An.push(e)}function pd(e,t,i,o){var l=t.interleaved;return l===null?(i.next=i,Ca(t)):(i.next=l.next,l.next=i),t.interleaved=i,Ht(e,o)}function Ht(e,t){e.lanes|=t;var i=e.alternate;for(i!==null&&(i.lanes|=t),i=e,e=e.return;e!==null;)e.childLanes|=t,i=e.alternate,i!==null&&(i.childLanes|=t),i=e,e=e.return;return i.tag===3?i.stateNode:null}var cn=!1;function Ta(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function md(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Kt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function dn(e,t,i){var o=e.updateQueue;if(o===null)return null;if(o=o.shared,(ue&2)!==0){var l=o.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),o.pending=t,Ht(e,i)}return l=o.interleaved,l===null?(t.next=t,Ca(o)):(t.next=l.next,l.next=t),o.interleaved=t,Ht(e,i)}function gs(e,t,i){if(t=t.updateQueue,t!==null&&(t=t.shared,(i&4194240)!==0)){var o=t.lanes;o&=e.pendingLanes,i|=o,t.lanes=i,Fo(e,i)}}function gd(e,t){var i=e.updateQueue,o=e.alternate;if(o!==null&&(o=o.updateQueue,i===o)){var l=null,c=null;if(i=i.firstBaseUpdate,i!==null){do{var m={eventTime:i.eventTime,lane:i.lane,tag:i.tag,payload:i.payload,callback:i.callback,next:null};c===null?l=c=m:c=c.next=m,i=i.next}while(i!==null);c===null?l=c=t:c=c.next=t}else l=c=t;i={baseState:o.baseState,firstBaseUpdate:l,lastBaseUpdate:c,shared:o.shared,effects:o.effects},e.updateQueue=i;return}e=i.lastBaseUpdate,e===null?i.firstBaseUpdate=t:e.next=t,i.lastBaseUpdate=t}function ys(e,t,i,o){var l=e.updateQueue;cn=!1;var c=l.firstBaseUpdate,m=l.lastBaseUpdate,E=l.shared.pending;if(E!==null){l.shared.pending=null;var T=E,R=T.next;T.next=null,m===null?c=R:m.next=R,m=T;var b=e.alternate;b!==null&&(b=b.updateQueue,E=b.lastBaseUpdate,E!==m&&(E===null?b.firstBaseUpdate=R:E.next=R,b.lastBaseUpdate=T))}if(c!==null){var O=l.baseState;m=0,b=R=T=null,E=c;do{var V=E.lane,$=E.eventTime;if((o&V)===V){b!==null&&(b=b.next={eventTime:$,lane:0,tag:E.tag,payload:E.payload,callback:E.callback,next:null});e:{var K=e,Q=E;switch(V=t,$=i,Q.tag){case 1:if(K=Q.payload,typeof K=="function"){O=K.call($,O,V);break e}O=K;break e;case 3:K.flags=K.flags&-65537|128;case 0:if(K=Q.payload,V=typeof K=="function"?K.call($,O,V):K,V==null)break e;O=W({},O,V);break e;case 2:cn=!0}}E.callback!==null&&E.lane!==0&&(e.flags|=64,V=l.effects,V===null?l.effects=[E]:V.push(E))}else $={eventTime:$,lane:V,tag:E.tag,payload:E.payload,callback:E.callback,next:null},b===null?(R=b=$,T=O):b=b.next=$,m|=V;if(E=E.next,E===null){if(E=l.shared.pending,E===null)break;V=E,E=V.next,V.next=null,l.lastBaseUpdate=V,l.shared.pending=null}}while(!0);if(b===null&&(T=O),l.baseState=T,l.firstBaseUpdate=R,l.lastBaseUpdate=b,t=l.shared.interleaved,t!==null){l=t;do m|=l.lane,l=l.next;while(l!==t)}else c===null&&(l.shared.lanes=0);Vn|=m,e.lanes=m,e.memoizedState=O}}function yd(e,t,i){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var o=e[t],l=o.callback;if(l!==null){if(o.callback=null,o=i,typeof l!="function")throw Error(s(191,l));l.call(o)}}}var ti={},_t=an(ti),ni=an(ti),ri=an(ti);function Ln(e){if(e===ti)throw Error(s(174));return e}function ka(e,t){switch(ye(ri,t),ye(ni,e),ye(_t,ti),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Po(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Po(t,e)}xe(_t),ye(_t,t)}function fr(){xe(_t),xe(ni),xe(ri)}function vd(e){Ln(ri.current);var t=Ln(_t.current),i=Po(t,e.type);t!==i&&(ye(ni,e),ye(_t,i))}function Pa(e){ni.current===e&&(xe(_t),xe(ni))}var Ce=an(0);function vs(e){for(var t=e;t!==null;){if(t.tag===13){var i=t.memoizedState;if(i!==null&&(i=i.dehydrated,i===null||i.data==="$?"||i.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ja=[];function Na(){for(var e=0;e<ja.length;e++)ja[e]._workInProgressVersionPrimary=null;ja.length=0}var xs=Y.ReactCurrentDispatcher,Ra=Y.ReactCurrentBatchConfig,Dn=0,Te=null,Ve=null,be=null,ws=!1,ii=!1,si=0,Vg=0;function $e(){throw Error(s(321))}function Ma(e,t){if(t===null)return!1;for(var i=0;i<t.length&&i<e.length;i++)if(!Pt(e[i],t[i]))return!1;return!0}function Aa(e,t,i,o,l,c){if(Dn=c,Te=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,xs.current=e===null||e.memoizedState===null?Fg:Ig,e=i(o,l),ii){c=0;do{if(ii=!1,si=0,25<=c)throw Error(s(301));c+=1,be=Ve=null,t.updateQueue=null,xs.current=Bg,e=i(o,l)}while(ii)}if(xs.current=Cs,t=Ve!==null&&Ve.next!==null,Dn=0,be=Ve=Te=null,ws=!1,t)throw Error(s(300));return e}function La(){var e=si!==0;return si=0,e}function bt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return be===null?Te.memoizedState=be=e:be=be.next=e,be}function vt(){if(Ve===null){var e=Te.alternate;e=e!==null?e.memoizedState:null}else e=Ve.next;var t=be===null?Te.memoizedState:be.next;if(t!==null)be=t,Ve=e;else{if(e===null)throw Error(s(310));Ve=e,e={memoizedState:Ve.memoizedState,baseState:Ve.baseState,baseQueue:Ve.baseQueue,queue:Ve.queue,next:null},be===null?Te.memoizedState=be=e:be=be.next=e}return be}function oi(e,t){return typeof t=="function"?t(e):t}function Da(e){var t=vt(),i=t.queue;if(i===null)throw Error(s(311));i.lastRenderedReducer=e;var o=Ve,l=o.baseQueue,c=i.pending;if(c!==null){if(l!==null){var m=l.next;l.next=c.next,c.next=m}o.baseQueue=l=c,i.pending=null}if(l!==null){c=l.next,o=o.baseState;var E=m=null,T=null,R=c;do{var b=R.lane;if((Dn&b)===b)T!==null&&(T=T.next={lane:0,action:R.action,hasEagerState:R.hasEagerState,eagerState:R.eagerState,next:null}),o=R.hasEagerState?R.eagerState:e(o,R.action);else{var O={lane:b,action:R.action,hasEagerState:R.hasEagerState,eagerState:R.eagerState,next:null};T===null?(E=T=O,m=o):T=T.next=O,Te.lanes|=b,Vn|=b}R=R.next}while(R!==null&&R!==c);T===null?m=o:T.next=E,Pt(o,t.memoizedState)||(nt=!0),t.memoizedState=o,t.baseState=m,t.baseQueue=T,i.lastRenderedState=o}if(e=i.interleaved,e!==null){l=e;do c=l.lane,Te.lanes|=c,Vn|=c,l=l.next;while(l!==e)}else l===null&&(i.lanes=0);return[t.memoizedState,i.dispatch]}function Va(e){var t=vt(),i=t.queue;if(i===null)throw Error(s(311));i.lastRenderedReducer=e;var o=i.dispatch,l=i.pending,c=t.memoizedState;if(l!==null){i.pending=null;var m=l=l.next;do c=e(c,m.action),m=m.next;while(m!==l);Pt(c,t.memoizedState)||(nt=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),i.lastRenderedState=c}return[c,o]}function xd(){}function wd(e,t){var i=Te,o=vt(),l=t(),c=!Pt(o.memoizedState,l);if(c&&(o.memoizedState=l,nt=!0),o=o.queue,_a(Cd.bind(null,i,o,e),[e]),o.getSnapshot!==t||c||be!==null&&be.memoizedState.tag&1){if(i.flags|=2048,ai(9,Ed.bind(null,i,o,l,t),void 0,null),Oe===null)throw Error(s(349));(Dn&30)!==0||Sd(i,t,l)}return l}function Sd(e,t,i){e.flags|=16384,e={getSnapshot:t,value:i},t=Te.updateQueue,t===null?(t={lastEffect:null,stores:null},Te.updateQueue=t,t.stores=[e]):(i=t.stores,i===null?t.stores=[e]:i.push(e))}function Ed(e,t,i,o){t.value=i,t.getSnapshot=o,Td(t)&&kd(e)}function Cd(e,t,i){return i(function(){Td(t)&&kd(e)})}function Td(e){var t=e.getSnapshot;e=e.value;try{var i=t();return!Pt(e,i)}catch{return!0}}function kd(e){var t=Ht(e,1);t!==null&&At(t,e,1,-1)}function Pd(e){var t=bt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:oi,lastRenderedState:e},t.queue=e,e=e.dispatch=Og.bind(null,Te,e),[t.memoizedState,e]}function ai(e,t,i,o){return e={tag:e,create:t,destroy:i,deps:o,next:null},t=Te.updateQueue,t===null?(t={lastEffect:null,stores:null},Te.updateQueue=t,t.lastEffect=e.next=e):(i=t.lastEffect,i===null?t.lastEffect=e.next=e:(o=i.next,i.next=e,e.next=o,t.lastEffect=e)),e}function jd(){return vt().memoizedState}function Ss(e,t,i,o){var l=bt();Te.flags|=e,l.memoizedState=ai(1|t,i,void 0,o===void 0?null:o)}function Es(e,t,i,o){var l=vt();o=o===void 0?null:o;var c=void 0;if(Ve!==null){var m=Ve.memoizedState;if(c=m.destroy,o!==null&&Ma(o,m.deps)){l.memoizedState=ai(t,i,c,o);return}}Te.flags|=e,l.memoizedState=ai(1|t,i,c,o)}function Nd(e,t){return Ss(8390656,8,e,t)}function _a(e,t){return Es(2048,8,e,t)}function Rd(e,t){return Es(4,2,e,t)}function Md(e,t){return Es(4,4,e,t)}function Ad(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Ld(e,t,i){return i=i!=null?i.concat([e]):null,Es(4,4,Ad.bind(null,t,e),i)}function ba(){}function Dd(e,t){var i=vt();t=t===void 0?null:t;var o=i.memoizedState;return o!==null&&t!==null&&Ma(t,o[1])?o[0]:(i.memoizedState=[e,t],e)}function Vd(e,t){var i=vt();t=t===void 0?null:t;var o=i.memoizedState;return o!==null&&t!==null&&Ma(t,o[1])?o[0]:(e=e(),i.memoizedState=[e,t],e)}function _d(e,t,i){return(Dn&21)===0?(e.baseState&&(e.baseState=!1,nt=!0),e.memoizedState=i):(Pt(i,t)||(i=dc(),Te.lanes|=i,Vn|=i,e.baseState=!0),t)}function _g(e,t){var i=pe;pe=i!==0&&4>i?i:4,e(!0);var o=Ra.transition;Ra.transition={};try{e(!1),t()}finally{pe=i,Ra.transition=o}}function bd(){return vt().memoizedState}function bg(e,t,i){var o=mn(e);if(i={lane:o,action:i,hasEagerState:!1,eagerState:null,next:null},Od(e))Fd(t,i);else if(i=pd(e,t,i,o),i!==null){var l=Xe();At(i,e,o,l),Id(i,t,o)}}function Og(e,t,i){var o=mn(e),l={lane:o,action:i,hasEagerState:!1,eagerState:null,next:null};if(Od(e))Fd(t,l);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var m=t.lastRenderedState,E=c(m,i);if(l.hasEagerState=!0,l.eagerState=E,Pt(E,m)){var T=t.interleaved;T===null?(l.next=l,Ca(t)):(l.next=T.next,T.next=l),t.interleaved=l;return}}catch{}finally{}i=pd(e,t,l,o),i!==null&&(l=Xe(),At(i,e,o,l),Id(i,t,o))}}function Od(e){var t=e.alternate;return e===Te||t!==null&&t===Te}function Fd(e,t){ii=ws=!0;var i=e.pending;i===null?t.next=t:(t.next=i.next,i.next=t),e.pending=t}function Id(e,t,i){if((i&4194240)!==0){var o=t.lanes;o&=e.pendingLanes,i|=o,t.lanes=i,Fo(e,i)}}var Cs={readContext:yt,useCallback:$e,useContext:$e,useEffect:$e,useImperativeHandle:$e,useInsertionEffect:$e,useLayoutEffect:$e,useMemo:$e,useReducer:$e,useRef:$e,useState:$e,useDebugValue:$e,useDeferredValue:$e,useTransition:$e,useMutableSource:$e,useSyncExternalStore:$e,useId:$e,unstable_isNewReconciler:!1},Fg={readContext:yt,useCallback:function(e,t){return bt().memoizedState=[e,t===void 0?null:t],e},useContext:yt,useEffect:Nd,useImperativeHandle:function(e,t,i){return i=i!=null?i.concat([e]):null,Ss(4194308,4,Ad.bind(null,t,e),i)},useLayoutEffect:function(e,t){return Ss(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ss(4,2,e,t)},useMemo:function(e,t){var i=bt();return t=t===void 0?null:t,e=e(),i.memoizedState=[e,t],e},useReducer:function(e,t,i){var o=bt();return t=i!==void 0?i(t):t,o.memoizedState=o.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},o.queue=e,e=e.dispatch=bg.bind(null,Te,e),[o.memoizedState,e]},useRef:function(e){var t=bt();return e={current:e},t.memoizedState=e},useState:Pd,useDebugValue:ba,useDeferredValue:function(e){return bt().memoizedState=e},useTransition:function(){var e=Pd(!1),t=e[0];return e=_g.bind(null,e[1]),bt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,i){var o=Te,l=bt();if(Ee){if(i===void 0)throw Error(s(407));i=i()}else{if(i=t(),Oe===null)throw Error(s(349));(Dn&30)!==0||Sd(o,t,i)}l.memoizedState=i;var c={value:i,getSnapshot:t};return l.queue=c,Nd(Cd.bind(null,o,c,e),[e]),o.flags|=2048,ai(9,Ed.bind(null,o,c,i,t),void 0,null),i},useId:function(){var e=bt(),t=Oe.identifierPrefix;if(Ee){var i=Wt,o=$t;i=(o&~(1<<32-kt(o)-1)).toString(32)+i,t=":"+t+"R"+i,i=si++,0<i&&(t+="H"+i.toString(32)),t+=":"}else i=Vg++,t=":"+t+"r"+i.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Ig={readContext:yt,useCallback:Dd,useContext:yt,useEffect:_a,useImperativeHandle:Ld,useInsertionEffect:Rd,useLayoutEffect:Md,useMemo:Vd,useReducer:Da,useRef:jd,useState:function(){return Da(oi)},useDebugValue:ba,useDeferredValue:function(e){var t=vt();return _d(t,Ve.memoizedState,e)},useTransition:function(){var e=Da(oi)[0],t=vt().memoizedState;return[e,t]},useMutableSource:xd,useSyncExternalStore:wd,useId:bd,unstable_isNewReconciler:!1},Bg={readContext:yt,useCallback:Dd,useContext:yt,useEffect:_a,useImperativeHandle:Ld,useInsertionEffect:Rd,useLayoutEffect:Md,useMemo:Vd,useReducer:Va,useRef:jd,useState:function(){return Va(oi)},useDebugValue:ba,useDeferredValue:function(e){var t=vt();return Ve===null?t.memoizedState=e:_d(t,Ve.memoizedState,e)},useTransition:function(){var e=Va(oi)[0],t=vt().memoizedState;return[e,t]},useMutableSource:xd,useSyncExternalStore:wd,useId:bd,unstable_isNewReconciler:!1};function Nt(e,t){if(e&&e.defaultProps){t=W({},t),e=e.defaultProps;for(var i in e)t[i]===void 0&&(t[i]=e[i]);return t}return t}function Oa(e,t,i,o){t=e.memoizedState,i=i(o,t),i=i==null?t:W({},t,i),e.memoizedState=i,e.lanes===0&&(e.updateQueue.baseState=i)}var Ts={isMounted:function(e){return(e=e._reactInternals)?Pn(e)===e:!1},enqueueSetState:function(e,t,i){e=e._reactInternals;var o=Xe(),l=mn(e),c=Kt(o,l);c.payload=t,i!=null&&(c.callback=i),t=dn(e,c,l),t!==null&&(At(t,e,l,o),gs(t,e,l))},enqueueReplaceState:function(e,t,i){e=e._reactInternals;var o=Xe(),l=mn(e),c=Kt(o,l);c.tag=1,c.payload=t,i!=null&&(c.callback=i),t=dn(e,c,l),t!==null&&(At(t,e,l,o),gs(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var i=Xe(),o=mn(e),l=Kt(i,o);l.tag=2,t!=null&&(l.callback=t),t=dn(e,l,o),t!==null&&(At(t,e,o,i),gs(t,e,o))}};function Bd(e,t,i,o,l,c,m){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(o,c,m):t.prototype&&t.prototype.isPureReactComponent?!Qr(i,o)||!Qr(l,c):!0}function zd(e,t,i){var o=!1,l=ln,c=t.contextType;return typeof c=="object"&&c!==null?c=yt(c):(l=tt(t)?Nn:Ue.current,o=t.contextTypes,c=(o=o!=null)?sr(e,l):ln),t=new t(i,c),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ts,e.stateNode=t,t._reactInternals=e,o&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=c),t}function Ud(e,t,i,o){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(i,o),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(i,o),t.state!==e&&Ts.enqueueReplaceState(t,t.state,null)}function Fa(e,t,i,o){var l=e.stateNode;l.props=i,l.state=e.memoizedState,l.refs={},Ta(e);var c=t.contextType;typeof c=="object"&&c!==null?l.context=yt(c):(c=tt(t)?Nn:Ue.current,l.context=sr(e,c)),l.state=e.memoizedState,c=t.getDerivedStateFromProps,typeof c=="function"&&(Oa(e,t,c,i),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&Ts.enqueueReplaceState(l,l.state,null),ys(e,i,l,o),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function hr(e,t){try{var i="",o=t;do i+=ce(o),o=o.return;while(o);var l=i}catch(c){l=`
Error generating stack: `+c.message+`
`+c.stack}return{value:e,source:t,stack:l,digest:null}}function Ia(e,t,i){return{value:e,source:null,stack:i??null,digest:t??null}}function Ba(e,t){try{console.error(t.value)}catch(i){setTimeout(function(){throw i})}}var zg=typeof WeakMap=="function"?WeakMap:Map;function $d(e,t,i){i=Kt(-1,i),i.tag=3,i.payload={element:null};var o=t.value;return i.callback=function(){As||(As=!0,tl=o),Ba(e,t)},i}function Wd(e,t,i){i=Kt(-1,i),i.tag=3;var o=e.type.getDerivedStateFromError;if(typeof o=="function"){var l=t.value;i.payload=function(){return o(l)},i.callback=function(){Ba(e,t)}}var c=e.stateNode;return c!==null&&typeof c.componentDidCatch=="function"&&(i.callback=function(){Ba(e,t),typeof o!="function"&&(hn===null?hn=new Set([this]):hn.add(this));var m=t.stack;this.componentDidCatch(t.value,{componentStack:m!==null?m:""})}),i}function Hd(e,t,i){var o=e.pingCache;if(o===null){o=e.pingCache=new zg;var l=new Set;o.set(t,l)}else l=o.get(t),l===void 0&&(l=new Set,o.set(t,l));l.has(i)||(l.add(i),e=ty.bind(null,e,t,i),t.then(e,e))}function Kd(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Qd(e,t,i,o,l){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,i.flags|=131072,i.flags&=-52805,i.tag===1&&(i.alternate===null?i.tag=17:(t=Kt(-1,1),t.tag=2,dn(i,t,1))),i.lanes|=1),e):(e.flags|=65536,e.lanes=l,e)}var Ug=Y.ReactCurrentOwner,nt=!1;function Ye(e,t,i,o){t.child=e===null?hd(t,null,i,o):ur(t,e.child,i,o)}function Gd(e,t,i,o,l){i=i.render;var c=t.ref;return dr(t,l),o=Aa(e,t,i,o,c,l),i=La(),e!==null&&!nt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,Qt(e,t,l)):(Ee&&i&&pa(t),t.flags|=1,Ye(e,t,o,l),t.child)}function Yd(e,t,i,o,l){if(e===null){var c=i.type;return typeof c=="function"&&!ll(c)&&c.defaultProps===void 0&&i.compare===null&&i.defaultProps===void 0?(t.tag=15,t.type=c,Xd(e,t,c,o,l)):(e=Os(i.type,null,o,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,(e.lanes&l)===0){var m=c.memoizedProps;if(i=i.compare,i=i!==null?i:Qr,i(m,o)&&e.ref===t.ref)return Qt(e,t,l)}return t.flags|=1,e=yn(c,o),e.ref=t.ref,e.return=t,t.child=e}function Xd(e,t,i,o,l){if(e!==null){var c=e.memoizedProps;if(Qr(c,o)&&e.ref===t.ref)if(nt=!1,t.pendingProps=o=c,(e.lanes&l)!==0)(e.flags&131072)!==0&&(nt=!0);else return t.lanes=e.lanes,Qt(e,t,l)}return za(e,t,i,o,l)}function Zd(e,t,i){var o=t.pendingProps,l=o.children,c=e!==null?e.memoizedState:null;if(o.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ye(mr,pt),pt|=i;else{if((i&1073741824)===0)return e=c!==null?c.baseLanes|i:i,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ye(mr,pt),pt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},o=c!==null?c.baseLanes:i,ye(mr,pt),pt|=o}else c!==null?(o=c.baseLanes|i,t.memoizedState=null):o=i,ye(mr,pt),pt|=o;return Ye(e,t,l,i),t.child}function qd(e,t){var i=t.ref;(e===null&&i!==null||e!==null&&e.ref!==i)&&(t.flags|=512,t.flags|=2097152)}function za(e,t,i,o,l){var c=tt(i)?Nn:Ue.current;return c=sr(t,c),dr(t,l),i=Aa(e,t,i,o,c,l),o=La(),e!==null&&!nt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,Qt(e,t,l)):(Ee&&o&&pa(t),t.flags|=1,Ye(e,t,i,l),t.child)}function Jd(e,t,i,o,l){if(tt(i)){var c=!0;ls(t)}else c=!1;if(dr(t,l),t.stateNode===null)Ps(e,t),zd(t,i,o),Fa(t,i,o,l),o=!0;else if(e===null){var m=t.stateNode,E=t.memoizedProps;m.props=E;var T=m.context,R=i.contextType;typeof R=="object"&&R!==null?R=yt(R):(R=tt(i)?Nn:Ue.current,R=sr(t,R));var b=i.getDerivedStateFromProps,O=typeof b=="function"||typeof m.getSnapshotBeforeUpdate=="function";O||typeof m.UNSAFE_componentWillReceiveProps!="function"&&typeof m.componentWillReceiveProps!="function"||(E!==o||T!==R)&&Ud(t,m,o,R),cn=!1;var V=t.memoizedState;m.state=V,ys(t,o,m,l),T=t.memoizedState,E!==o||V!==T||et.current||cn?(typeof b=="function"&&(Oa(t,i,b,o),T=t.memoizedState),(E=cn||Bd(t,i,E,o,V,T,R))?(O||typeof m.UNSAFE_componentWillMount!="function"&&typeof m.componentWillMount!="function"||(typeof m.componentWillMount=="function"&&m.componentWillMount(),typeof m.UNSAFE_componentWillMount=="function"&&m.UNSAFE_componentWillMount()),typeof m.componentDidMount=="function"&&(t.flags|=4194308)):(typeof m.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=o,t.memoizedState=T),m.props=o,m.state=T,m.context=R,o=E):(typeof m.componentDidMount=="function"&&(t.flags|=4194308),o=!1)}else{m=t.stateNode,md(e,t),E=t.memoizedProps,R=t.type===t.elementType?E:Nt(t.type,E),m.props=R,O=t.pendingProps,V=m.context,T=i.contextType,typeof T=="object"&&T!==null?T=yt(T):(T=tt(i)?Nn:Ue.current,T=sr(t,T));var $=i.getDerivedStateFromProps;(b=typeof $=="function"||typeof m.getSnapshotBeforeUpdate=="function")||typeof m.UNSAFE_componentWillReceiveProps!="function"&&typeof m.componentWillReceiveProps!="function"||(E!==O||V!==T)&&Ud(t,m,o,T),cn=!1,V=t.memoizedState,m.state=V,ys(t,o,m,l);var K=t.memoizedState;E!==O||V!==K||et.current||cn?(typeof $=="function"&&(Oa(t,i,$,o),K=t.memoizedState),(R=cn||Bd(t,i,R,o,V,K,T)||!1)?(b||typeof m.UNSAFE_componentWillUpdate!="function"&&typeof m.componentWillUpdate!="function"||(typeof m.componentWillUpdate=="function"&&m.componentWillUpdate(o,K,T),typeof m.UNSAFE_componentWillUpdate=="function"&&m.UNSAFE_componentWillUpdate(o,K,T)),typeof m.componentDidUpdate=="function"&&(t.flags|=4),typeof m.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof m.componentDidUpdate!="function"||E===e.memoizedProps&&V===e.memoizedState||(t.flags|=4),typeof m.getSnapshotBeforeUpdate!="function"||E===e.memoizedProps&&V===e.memoizedState||(t.flags|=1024),t.memoizedProps=o,t.memoizedState=K),m.props=o,m.state=K,m.context=T,o=R):(typeof m.componentDidUpdate!="function"||E===e.memoizedProps&&V===e.memoizedState||(t.flags|=4),typeof m.getSnapshotBeforeUpdate!="function"||E===e.memoizedProps&&V===e.memoizedState||(t.flags|=1024),o=!1)}return Ua(e,t,i,o,c,l)}function Ua(e,t,i,o,l,c){qd(e,t);var m=(t.flags&128)!==0;if(!o&&!m)return l&&id(t,i,!1),Qt(e,t,c);o=t.stateNode,Ug.current=t;var E=m&&typeof i.getDerivedStateFromError!="function"?null:o.render();return t.flags|=1,e!==null&&m?(t.child=ur(t,e.child,null,c),t.child=ur(t,null,E,c)):Ye(e,t,E,c),t.memoizedState=o.state,l&&id(t,i,!0),t.child}function ef(e){var t=e.stateNode;t.pendingContext?nd(e,t.pendingContext,t.pendingContext!==t.context):t.context&&nd(e,t.context,!1),ka(e,t.containerInfo)}function tf(e,t,i,o,l){return lr(),va(l),t.flags|=256,Ye(e,t,i,o),t.child}var $a={dehydrated:null,treeContext:null,retryLane:0};function Wa(e){return{baseLanes:e,cachePool:null,transitions:null}}function nf(e,t,i){var o=t.pendingProps,l=Ce.current,c=!1,m=(t.flags&128)!==0,E;if((E=m)||(E=e!==null&&e.memoizedState===null?!1:(l&2)!==0),E?(c=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),ye(Ce,l&1),e===null)return ya(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(m=o.children,e=o.fallback,c?(o=t.mode,c=t.child,m={mode:"hidden",children:m},(o&1)===0&&c!==null?(c.childLanes=0,c.pendingProps=m):c=Fs(m,o,0,null),e=Fn(e,o,i,null),c.return=t,e.return=t,c.sibling=e,t.child=c,t.child.memoizedState=Wa(i),t.memoizedState=$a,e):Ha(t,m));if(l=e.memoizedState,l!==null&&(E=l.dehydrated,E!==null))return $g(e,t,m,o,E,l,i);if(c){c=o.fallback,m=t.mode,l=e.child,E=l.sibling;var T={mode:"hidden",children:o.children};return(m&1)===0&&t.child!==l?(o=t.child,o.childLanes=0,o.pendingProps=T,t.deletions=null):(o=yn(l,T),o.subtreeFlags=l.subtreeFlags&14680064),E!==null?c=yn(E,c):(c=Fn(c,m,i,null),c.flags|=2),c.return=t,o.return=t,o.sibling=c,t.child=o,o=c,c=t.child,m=e.child.memoizedState,m=m===null?Wa(i):{baseLanes:m.baseLanes|i,cachePool:null,transitions:m.transitions},c.memoizedState=m,c.childLanes=e.childLanes&~i,t.memoizedState=$a,o}return c=e.child,e=c.sibling,o=yn(c,{mode:"visible",children:o.children}),(t.mode&1)===0&&(o.lanes=i),o.return=t,o.sibling=null,e!==null&&(i=t.deletions,i===null?(t.deletions=[e],t.flags|=16):i.push(e)),t.child=o,t.memoizedState=null,o}function Ha(e,t){return t=Fs({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function ks(e,t,i,o){return o!==null&&va(o),ur(t,e.child,null,i),e=Ha(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function $g(e,t,i,o,l,c,m){if(i)return t.flags&256?(t.flags&=-257,o=Ia(Error(s(422))),ks(e,t,m,o)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(c=o.fallback,l=t.mode,o=Fs({mode:"visible",children:o.children},l,0,null),c=Fn(c,l,m,null),c.flags|=2,o.return=t,c.return=t,o.sibling=c,t.child=o,(t.mode&1)!==0&&ur(t,e.child,null,m),t.child.memoizedState=Wa(m),t.memoizedState=$a,c);if((t.mode&1)===0)return ks(e,t,m,null);if(l.data==="$!"){if(o=l.nextSibling&&l.nextSibling.dataset,o)var E=o.dgst;return o=E,c=Error(s(419)),o=Ia(c,o,void 0),ks(e,t,m,o)}if(E=(m&e.childLanes)!==0,nt||E){if(o=Oe,o!==null){switch(m&-m){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=(l&(o.suspendedLanes|m))!==0?0:l,l!==0&&l!==c.retryLane&&(c.retryLane=l,Ht(e,l),At(o,e,l,-1))}return al(),o=Ia(Error(s(421))),ks(e,t,m,o)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=ny.bind(null,e),l._reactRetry=t,null):(e=c.treeContext,ht=on(l.nextSibling),ft=t,Ee=!0,jt=null,e!==null&&(mt[gt++]=$t,mt[gt++]=Wt,mt[gt++]=Rn,$t=e.id,Wt=e.overflow,Rn=t),t=Ha(t,o.children),t.flags|=4096,t)}function rf(e,t,i){e.lanes|=t;var o=e.alternate;o!==null&&(o.lanes|=t),Ea(e.return,t,i)}function Ka(e,t,i,o,l){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:o,tail:i,tailMode:l}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=o,c.tail=i,c.tailMode=l)}function sf(e,t,i){var o=t.pendingProps,l=o.revealOrder,c=o.tail;if(Ye(e,t,o.children,i),o=Ce.current,(o&2)!==0)o=o&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&rf(e,i,t);else if(e.tag===19)rf(e,i,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}o&=1}if(ye(Ce,o),(t.mode&1)===0)t.memoizedState=null;else switch(l){case"forwards":for(i=t.child,l=null;i!==null;)e=i.alternate,e!==null&&vs(e)===null&&(l=i),i=i.sibling;i=l,i===null?(l=t.child,t.child=null):(l=i.sibling,i.sibling=null),Ka(t,!1,l,i,c);break;case"backwards":for(i=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&vs(e)===null){t.child=l;break}e=l.sibling,l.sibling=i,i=l,l=e}Ka(t,!0,i,null,c);break;case"together":Ka(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ps(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Qt(e,t,i){if(e!==null&&(t.dependencies=e.dependencies),Vn|=t.lanes,(i&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(s(153));if(t.child!==null){for(e=t.child,i=yn(e,e.pendingProps),t.child=i,i.return=t;e.sibling!==null;)e=e.sibling,i=i.sibling=yn(e,e.pendingProps),i.return=t;i.sibling=null}return t.child}function Wg(e,t,i){switch(t.tag){case 3:ef(t),lr();break;case 5:vd(t);break;case 1:tt(t.type)&&ls(t);break;case 4:ka(t,t.stateNode.containerInfo);break;case 10:var o=t.type._context,l=t.memoizedProps.value;ye(ps,o._currentValue),o._currentValue=l;break;case 13:if(o=t.memoizedState,o!==null)return o.dehydrated!==null?(ye(Ce,Ce.current&1),t.flags|=128,null):(i&t.child.childLanes)!==0?nf(e,t,i):(ye(Ce,Ce.current&1),e=Qt(e,t,i),e!==null?e.sibling:null);ye(Ce,Ce.current&1);break;case 19:if(o=(i&t.childLanes)!==0,(e.flags&128)!==0){if(o)return sf(e,t,i);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),ye(Ce,Ce.current),o)break;return null;case 22:case 23:return t.lanes=0,Zd(e,t,i)}return Qt(e,t,i)}var of,Qa,af,lf;of=function(e,t){for(var i=t.child;i!==null;){if(i.tag===5||i.tag===6)e.appendChild(i.stateNode);else if(i.tag!==4&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===t)break;for(;i.sibling===null;){if(i.return===null||i.return===t)return;i=i.return}i.sibling.return=i.return,i=i.sibling}},Qa=function(){},af=function(e,t,i,o){var l=e.memoizedProps;if(l!==o){e=t.stateNode,Ln(_t.current);var c=null;switch(i){case"input":l=Eo(e,l),o=Eo(e,o),c=[];break;case"select":l=W({},l,{value:void 0}),o=W({},o,{value:void 0}),c=[];break;case"textarea":l=ko(e,l),o=ko(e,o),c=[];break;default:typeof l.onClick!="function"&&typeof o.onClick=="function"&&(e.onclick=ss)}jo(i,o);var m;i=null;for(R in l)if(!o.hasOwnProperty(R)&&l.hasOwnProperty(R)&&l[R]!=null)if(R==="style"){var E=l[R];for(m in E)E.hasOwnProperty(m)&&(i||(i={}),i[m]="")}else R!=="dangerouslySetInnerHTML"&&R!=="children"&&R!=="suppressContentEditableWarning"&&R!=="suppressHydrationWarning"&&R!=="autoFocus"&&(u.hasOwnProperty(R)?c||(c=[]):(c=c||[]).push(R,null));for(R in o){var T=o[R];if(E=l!=null?l[R]:void 0,o.hasOwnProperty(R)&&T!==E&&(T!=null||E!=null))if(R==="style")if(E){for(m in E)!E.hasOwnProperty(m)||T&&T.hasOwnProperty(m)||(i||(i={}),i[m]="");for(m in T)T.hasOwnProperty(m)&&E[m]!==T[m]&&(i||(i={}),i[m]=T[m])}else i||(c||(c=[]),c.push(R,i)),i=T;else R==="dangerouslySetInnerHTML"?(T=T?T.__html:void 0,E=E?E.__html:void 0,T!=null&&E!==T&&(c=c||[]).push(R,T)):R==="children"?typeof T!="string"&&typeof T!="number"||(c=c||[]).push(R,""+T):R!=="suppressContentEditableWarning"&&R!=="suppressHydrationWarning"&&(u.hasOwnProperty(R)?(T!=null&&R==="onScroll"&&ve("scroll",e),c||E===T||(c=[])):(c=c||[]).push(R,T))}i&&(c=c||[]).push("style",i);var R=c;(t.updateQueue=R)&&(t.flags|=4)}},lf=function(e,t,i,o){i!==o&&(t.flags|=4)};function li(e,t){if(!Ee)switch(e.tailMode){case"hidden":t=e.tail;for(var i=null;t!==null;)t.alternate!==null&&(i=t),t=t.sibling;i===null?e.tail=null:i.sibling=null;break;case"collapsed":i=e.tail;for(var o=null;i!==null;)i.alternate!==null&&(o=i),i=i.sibling;o===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:o.sibling=null}}function We(e){var t=e.alternate!==null&&e.alternate.child===e.child,i=0,o=0;if(t)for(var l=e.child;l!==null;)i|=l.lanes|l.childLanes,o|=l.subtreeFlags&14680064,o|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)i|=l.lanes|l.childLanes,o|=l.subtreeFlags,o|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=o,e.childLanes=i,t}function Hg(e,t,i){var o=t.pendingProps;switch(ma(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return We(t),null;case 1:return tt(t.type)&&as(),We(t),null;case 3:return o=t.stateNode,fr(),xe(et),xe(Ue),Na(),o.pendingContext&&(o.context=o.pendingContext,o.pendingContext=null),(e===null||e.child===null)&&(fs(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,jt!==null&&(il(jt),jt=null))),Qa(e,t),We(t),null;case 5:Pa(t);var l=Ln(ri.current);if(i=t.type,e!==null&&t.stateNode!=null)af(e,t,i,o,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!o){if(t.stateNode===null)throw Error(s(166));return We(t),null}if(e=Ln(_t.current),fs(t)){o=t.stateNode,i=t.type;var c=t.memoizedProps;switch(o[Vt]=t,o[qr]=c,e=(t.mode&1)!==0,i){case"dialog":ve("cancel",o),ve("close",o);break;case"iframe":case"object":case"embed":ve("load",o);break;case"video":case"audio":for(l=0;l<Yr.length;l++)ve(Yr[l],o);break;case"source":ve("error",o);break;case"img":case"image":case"link":ve("error",o),ve("load",o);break;case"details":ve("toggle",o);break;case"input":zu(o,c),ve("invalid",o);break;case"select":o._wrapperState={wasMultiple:!!c.multiple},ve("invalid",o);break;case"textarea":Wu(o,c),ve("invalid",o)}jo(i,c),l=null;for(var m in c)if(c.hasOwnProperty(m)){var E=c[m];m==="children"?typeof E=="string"?o.textContent!==E&&(c.suppressHydrationWarning!==!0&&is(o.textContent,E,e),l=["children",E]):typeof E=="number"&&o.textContent!==""+E&&(c.suppressHydrationWarning!==!0&&is(o.textContent,E,e),l=["children",""+E]):u.hasOwnProperty(m)&&E!=null&&m==="onScroll"&&ve("scroll",o)}switch(i){case"input":_i(o),$u(o,c,!0);break;case"textarea":_i(o),Ku(o);break;case"select":case"option":break;default:typeof c.onClick=="function"&&(o.onclick=ss)}o=l,t.updateQueue=o,o!==null&&(t.flags|=4)}else{m=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Qu(i)),e==="http://www.w3.org/1999/xhtml"?i==="script"?(e=m.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof o.is=="string"?e=m.createElement(i,{is:o.is}):(e=m.createElement(i),i==="select"&&(m=e,o.multiple?m.multiple=!0:o.size&&(m.size=o.size))):e=m.createElementNS(e,i),e[Vt]=t,e[qr]=o,of(e,t,!1,!1),t.stateNode=e;e:{switch(m=No(i,o),i){case"dialog":ve("cancel",e),ve("close",e),l=o;break;case"iframe":case"object":case"embed":ve("load",e),l=o;break;case"video":case"audio":for(l=0;l<Yr.length;l++)ve(Yr[l],e);l=o;break;case"source":ve("error",e),l=o;break;case"img":case"image":case"link":ve("error",e),ve("load",e),l=o;break;case"details":ve("toggle",e),l=o;break;case"input":zu(e,o),l=Eo(e,o),ve("invalid",e);break;case"option":l=o;break;case"select":e._wrapperState={wasMultiple:!!o.multiple},l=W({},o,{value:void 0}),ve("invalid",e);break;case"textarea":Wu(e,o),l=ko(e,o),ve("invalid",e);break;default:l=o}jo(i,l),E=l;for(c in E)if(E.hasOwnProperty(c)){var T=E[c];c==="style"?Xu(e,T):c==="dangerouslySetInnerHTML"?(T=T?T.__html:void 0,T!=null&&Gu(e,T)):c==="children"?typeof T=="string"?(i!=="textarea"||T!=="")&&Mr(e,T):typeof T=="number"&&Mr(e,""+T):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(u.hasOwnProperty(c)?T!=null&&c==="onScroll"&&ve("scroll",e):T!=null&&F(e,c,T,m))}switch(i){case"input":_i(e),$u(e,o,!1);break;case"textarea":_i(e),Ku(e);break;case"option":o.value!=null&&e.setAttribute("value",""+he(o.value));break;case"select":e.multiple=!!o.multiple,c=o.value,c!=null?Qn(e,!!o.multiple,c,!1):o.defaultValue!=null&&Qn(e,!!o.multiple,o.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=ss)}switch(i){case"button":case"input":case"select":case"textarea":o=!!o.autoFocus;break e;case"img":o=!0;break e;default:o=!1}}o&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return We(t),null;case 6:if(e&&t.stateNode!=null)lf(e,t,e.memoizedProps,o);else{if(typeof o!="string"&&t.stateNode===null)throw Error(s(166));if(i=Ln(ri.current),Ln(_t.current),fs(t)){if(o=t.stateNode,i=t.memoizedProps,o[Vt]=t,(c=o.nodeValue!==i)&&(e=ft,e!==null))switch(e.tag){case 3:is(o.nodeValue,i,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&is(o.nodeValue,i,(e.mode&1)!==0)}c&&(t.flags|=4)}else o=(i.nodeType===9?i:i.ownerDocument).createTextNode(o),o[Vt]=t,t.stateNode=o}return We(t),null;case 13:if(xe(Ce),o=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Ee&&ht!==null&&(t.mode&1)!==0&&(t.flags&128)===0)cd(),lr(),t.flags|=98560,c=!1;else if(c=fs(t),o!==null&&o.dehydrated!==null){if(e===null){if(!c)throw Error(s(318));if(c=t.memoizedState,c=c!==null?c.dehydrated:null,!c)throw Error(s(317));c[Vt]=t}else lr(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;We(t),c=!1}else jt!==null&&(il(jt),jt=null),c=!0;if(!c)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=i,t):(o=o!==null,o!==(e!==null&&e.memoizedState!==null)&&o&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(Ce.current&1)!==0?_e===0&&(_e=3):al())),t.updateQueue!==null&&(t.flags|=4),We(t),null);case 4:return fr(),Qa(e,t),e===null&&Xr(t.stateNode.containerInfo),We(t),null;case 10:return Sa(t.type._context),We(t),null;case 17:return tt(t.type)&&as(),We(t),null;case 19:if(xe(Ce),c=t.memoizedState,c===null)return We(t),null;if(o=(t.flags&128)!==0,m=c.rendering,m===null)if(o)li(c,!1);else{if(_e!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(m=vs(e),m!==null){for(t.flags|=128,li(c,!1),o=m.updateQueue,o!==null&&(t.updateQueue=o,t.flags|=4),t.subtreeFlags=0,o=i,i=t.child;i!==null;)c=i,e=o,c.flags&=14680066,m=c.alternate,m===null?(c.childLanes=0,c.lanes=e,c.child=null,c.subtreeFlags=0,c.memoizedProps=null,c.memoizedState=null,c.updateQueue=null,c.dependencies=null,c.stateNode=null):(c.childLanes=m.childLanes,c.lanes=m.lanes,c.child=m.child,c.subtreeFlags=0,c.deletions=null,c.memoizedProps=m.memoizedProps,c.memoizedState=m.memoizedState,c.updateQueue=m.updateQueue,c.type=m.type,e=m.dependencies,c.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),i=i.sibling;return ye(Ce,Ce.current&1|2),t.child}e=e.sibling}c.tail!==null&&Re()>gr&&(t.flags|=128,o=!0,li(c,!1),t.lanes=4194304)}else{if(!o)if(e=vs(m),e!==null){if(t.flags|=128,o=!0,i=e.updateQueue,i!==null&&(t.updateQueue=i,t.flags|=4),li(c,!0),c.tail===null&&c.tailMode==="hidden"&&!m.alternate&&!Ee)return We(t),null}else 2*Re()-c.renderingStartTime>gr&&i!==1073741824&&(t.flags|=128,o=!0,li(c,!1),t.lanes=4194304);c.isBackwards?(m.sibling=t.child,t.child=m):(i=c.last,i!==null?i.sibling=m:t.child=m,c.last=m)}return c.tail!==null?(t=c.tail,c.rendering=t,c.tail=t.sibling,c.renderingStartTime=Re(),t.sibling=null,i=Ce.current,ye(Ce,o?i&1|2:i&1),t):(We(t),null);case 22:case 23:return ol(),o=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==o&&(t.flags|=8192),o&&(t.mode&1)!==0?(pt&1073741824)!==0&&(We(t),t.subtreeFlags&6&&(t.flags|=8192)):We(t),null;case 24:return null;case 25:return null}throw Error(s(156,t.tag))}function Kg(e,t){switch(ma(t),t.tag){case 1:return tt(t.type)&&as(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return fr(),xe(et),xe(Ue),Na(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return Pa(t),null;case 13:if(xe(Ce),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(s(340));lr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return xe(Ce),null;case 4:return fr(),null;case 10:return Sa(t.type._context),null;case 22:case 23:return ol(),null;case 24:return null;default:return null}}var js=!1,He=!1,Qg=typeof WeakSet=="function"?WeakSet:Set,H=null;function pr(e,t){var i=e.ref;if(i!==null)if(typeof i=="function")try{i(null)}catch(o){je(e,t,o)}else i.current=null}function Ga(e,t,i){try{i()}catch(o){je(e,t,o)}}var uf=!1;function Gg(e,t){if(oa=Qi,e=Bc(),qo(e)){if("selectionStart"in e)var i={start:e.selectionStart,end:e.selectionEnd};else e:{i=(i=e.ownerDocument)&&i.defaultView||window;var o=i.getSelection&&i.getSelection();if(o&&o.rangeCount!==0){i=o.anchorNode;var l=o.anchorOffset,c=o.focusNode;o=o.focusOffset;try{i.nodeType,c.nodeType}catch{i=null;break e}var m=0,E=-1,T=-1,R=0,b=0,O=e,V=null;t:for(;;){for(var $;O!==i||l!==0&&O.nodeType!==3||(E=m+l),O!==c||o!==0&&O.nodeType!==3||(T=m+o),O.nodeType===3&&(m+=O.nodeValue.length),($=O.firstChild)!==null;)V=O,O=$;for(;;){if(O===e)break t;if(V===i&&++R===l&&(E=m),V===c&&++b===o&&(T=m),($=O.nextSibling)!==null)break;O=V,V=O.parentNode}O=$}i=E===-1||T===-1?null:{start:E,end:T}}else i=null}i=i||{start:0,end:0}}else i=null;for(aa={focusedElem:e,selectionRange:i},Qi=!1,H=t;H!==null;)if(t=H,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,H=e;else for(;H!==null;){t=H;try{var K=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(K!==null){var Q=K.memoizedProps,Me=K.memoizedState,j=t.stateNode,k=j.getSnapshotBeforeUpdate(t.elementType===t.type?Q:Nt(t.type,Q),Me);j.__reactInternalSnapshotBeforeUpdate=k}break;case 3:var N=t.stateNode.containerInfo;N.nodeType===1?N.textContent="":N.nodeType===9&&N.documentElement&&N.removeChild(N.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(s(163))}}catch(I){je(t,t.return,I)}if(e=t.sibling,e!==null){e.return=t.return,H=e;break}H=t.return}return K=uf,uf=!1,K}function ui(e,t,i){var o=t.updateQueue;if(o=o!==null?o.lastEffect:null,o!==null){var l=o=o.next;do{if((l.tag&e)===e){var c=l.destroy;l.destroy=void 0,c!==void 0&&Ga(t,i,c)}l=l.next}while(l!==o)}}function Ns(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var i=t=t.next;do{if((i.tag&e)===e){var o=i.create;i.destroy=o()}i=i.next}while(i!==t)}}function Ya(e){var t=e.ref;if(t!==null){var i=e.stateNode;switch(e.tag){case 5:e=i;break;default:e=i}typeof t=="function"?t(e):t.current=e}}function cf(e){var t=e.alternate;t!==null&&(e.alternate=null,cf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Vt],delete t[qr],delete t[da],delete t[Mg],delete t[Ag])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function df(e){return e.tag===5||e.tag===3||e.tag===4}function ff(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||df(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Xa(e,t,i){var o=e.tag;if(o===5||o===6)e=e.stateNode,t?i.nodeType===8?i.parentNode.insertBefore(e,t):i.insertBefore(e,t):(i.nodeType===8?(t=i.parentNode,t.insertBefore(e,i)):(t=i,t.appendChild(e)),i=i._reactRootContainer,i!=null||t.onclick!==null||(t.onclick=ss));else if(o!==4&&(e=e.child,e!==null))for(Xa(e,t,i),e=e.sibling;e!==null;)Xa(e,t,i),e=e.sibling}function Za(e,t,i){var o=e.tag;if(o===5||o===6)e=e.stateNode,t?i.insertBefore(e,t):i.appendChild(e);else if(o!==4&&(e=e.child,e!==null))for(Za(e,t,i),e=e.sibling;e!==null;)Za(e,t,i),e=e.sibling}var Ie=null,Rt=!1;function fn(e,t,i){for(i=i.child;i!==null;)hf(e,t,i),i=i.sibling}function hf(e,t,i){if(Dt&&typeof Dt.onCommitFiberUnmount=="function")try{Dt.onCommitFiberUnmount(zi,i)}catch{}switch(i.tag){case 5:He||pr(i,t);case 6:var o=Ie,l=Rt;Ie=null,fn(e,t,i),Ie=o,Rt=l,Ie!==null&&(Rt?(e=Ie,i=i.stateNode,e.nodeType===8?e.parentNode.removeChild(i):e.removeChild(i)):Ie.removeChild(i.stateNode));break;case 18:Ie!==null&&(Rt?(e=Ie,i=i.stateNode,e.nodeType===8?ca(e.parentNode,i):e.nodeType===1&&ca(e,i),zr(e)):ca(Ie,i.stateNode));break;case 4:o=Ie,l=Rt,Ie=i.stateNode.containerInfo,Rt=!0,fn(e,t,i),Ie=o,Rt=l;break;case 0:case 11:case 14:case 15:if(!He&&(o=i.updateQueue,o!==null&&(o=o.lastEffect,o!==null))){l=o=o.next;do{var c=l,m=c.destroy;c=c.tag,m!==void 0&&((c&2)!==0||(c&4)!==0)&&Ga(i,t,m),l=l.next}while(l!==o)}fn(e,t,i);break;case 1:if(!He&&(pr(i,t),o=i.stateNode,typeof o.componentWillUnmount=="function"))try{o.props=i.memoizedProps,o.state=i.memoizedState,o.componentWillUnmount()}catch(E){je(i,t,E)}fn(e,t,i);break;case 21:fn(e,t,i);break;case 22:i.mode&1?(He=(o=He)||i.memoizedState!==null,fn(e,t,i),He=o):fn(e,t,i);break;default:fn(e,t,i)}}function pf(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var i=e.stateNode;i===null&&(i=e.stateNode=new Qg),t.forEach(function(o){var l=ry.bind(null,e,o);i.has(o)||(i.add(o),o.then(l,l))})}}function Mt(e,t){var i=t.deletions;if(i!==null)for(var o=0;o<i.length;o++){var l=i[o];try{var c=e,m=t,E=m;e:for(;E!==null;){switch(E.tag){case 5:Ie=E.stateNode,Rt=!1;break e;case 3:Ie=E.stateNode.containerInfo,Rt=!0;break e;case 4:Ie=E.stateNode.containerInfo,Rt=!0;break e}E=E.return}if(Ie===null)throw Error(s(160));hf(c,m,l),Ie=null,Rt=!1;var T=l.alternate;T!==null&&(T.return=null),l.return=null}catch(R){je(l,t,R)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)mf(t,e),t=t.sibling}function mf(e,t){var i=e.alternate,o=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Mt(t,e),Ot(e),o&4){try{ui(3,e,e.return),Ns(3,e)}catch(Q){je(e,e.return,Q)}try{ui(5,e,e.return)}catch(Q){je(e,e.return,Q)}}break;case 1:Mt(t,e),Ot(e),o&512&&i!==null&&pr(i,i.return);break;case 5:if(Mt(t,e),Ot(e),o&512&&i!==null&&pr(i,i.return),e.flags&32){var l=e.stateNode;try{Mr(l,"")}catch(Q){je(e,e.return,Q)}}if(o&4&&(l=e.stateNode,l!=null)){var c=e.memoizedProps,m=i!==null?i.memoizedProps:c,E=e.type,T=e.updateQueue;if(e.updateQueue=null,T!==null)try{E==="input"&&c.type==="radio"&&c.name!=null&&Uu(l,c),No(E,m);var R=No(E,c);for(m=0;m<T.length;m+=2){var b=T[m],O=T[m+1];b==="style"?Xu(l,O):b==="dangerouslySetInnerHTML"?Gu(l,O):b==="children"?Mr(l,O):F(l,b,O,R)}switch(E){case"input":Co(l,c);break;case"textarea":Hu(l,c);break;case"select":var V=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!c.multiple;var $=c.value;$!=null?Qn(l,!!c.multiple,$,!1):V!==!!c.multiple&&(c.defaultValue!=null?Qn(l,!!c.multiple,c.defaultValue,!0):Qn(l,!!c.multiple,c.multiple?[]:"",!1))}l[qr]=c}catch(Q){je(e,e.return,Q)}}break;case 6:if(Mt(t,e),Ot(e),o&4){if(e.stateNode===null)throw Error(s(162));l=e.stateNode,c=e.memoizedProps;try{l.nodeValue=c}catch(Q){je(e,e.return,Q)}}break;case 3:if(Mt(t,e),Ot(e),o&4&&i!==null&&i.memoizedState.isDehydrated)try{zr(t.containerInfo)}catch(Q){je(e,e.return,Q)}break;case 4:Mt(t,e),Ot(e);break;case 13:Mt(t,e),Ot(e),l=e.child,l.flags&8192&&(c=l.memoizedState!==null,l.stateNode.isHidden=c,!c||l.alternate!==null&&l.alternate.memoizedState!==null||(el=Re())),o&4&&pf(e);break;case 22:if(b=i!==null&&i.memoizedState!==null,e.mode&1?(He=(R=He)||b,Mt(t,e),He=R):Mt(t,e),Ot(e),o&8192){if(R=e.memoizedState!==null,(e.stateNode.isHidden=R)&&!b&&(e.mode&1)!==0)for(H=e,b=e.child;b!==null;){for(O=H=b;H!==null;){switch(V=H,$=V.child,V.tag){case 0:case 11:case 14:case 15:ui(4,V,V.return);break;case 1:pr(V,V.return);var K=V.stateNode;if(typeof K.componentWillUnmount=="function"){o=V,i=V.return;try{t=o,K.props=t.memoizedProps,K.state=t.memoizedState,K.componentWillUnmount()}catch(Q){je(o,i,Q)}}break;case 5:pr(V,V.return);break;case 22:if(V.memoizedState!==null){vf(O);continue}}$!==null?($.return=V,H=$):vf(O)}b=b.sibling}e:for(b=null,O=e;;){if(O.tag===5){if(b===null){b=O;try{l=O.stateNode,R?(c=l.style,typeof c.setProperty=="function"?c.setProperty("display","none","important"):c.display="none"):(E=O.stateNode,T=O.memoizedProps.style,m=T!=null&&T.hasOwnProperty("display")?T.display:null,E.style.display=Yu("display",m))}catch(Q){je(e,e.return,Q)}}}else if(O.tag===6){if(b===null)try{O.stateNode.nodeValue=R?"":O.memoizedProps}catch(Q){je(e,e.return,Q)}}else if((O.tag!==22&&O.tag!==23||O.memoizedState===null||O===e)&&O.child!==null){O.child.return=O,O=O.child;continue}if(O===e)break e;for(;O.sibling===null;){if(O.return===null||O.return===e)break e;b===O&&(b=null),O=O.return}b===O&&(b=null),O.sibling.return=O.return,O=O.sibling}}break;case 19:Mt(t,e),Ot(e),o&4&&pf(e);break;case 21:break;default:Mt(t,e),Ot(e)}}function Ot(e){var t=e.flags;if(t&2){try{e:{for(var i=e.return;i!==null;){if(df(i)){var o=i;break e}i=i.return}throw Error(s(160))}switch(o.tag){case 5:var l=o.stateNode;o.flags&32&&(Mr(l,""),o.flags&=-33);var c=ff(e);Za(e,c,l);break;case 3:case 4:var m=o.stateNode.containerInfo,E=ff(e);Xa(e,E,m);break;default:throw Error(s(161))}}catch(T){je(e,e.return,T)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Yg(e,t,i){H=e,gf(e)}function gf(e,t,i){for(var o=(e.mode&1)!==0;H!==null;){var l=H,c=l.child;if(l.tag===22&&o){var m=l.memoizedState!==null||js;if(!m){var E=l.alternate,T=E!==null&&E.memoizedState!==null||He;E=js;var R=He;if(js=m,(He=T)&&!R)for(H=l;H!==null;)m=H,T=m.child,m.tag===22&&m.memoizedState!==null?xf(l):T!==null?(T.return=m,H=T):xf(l);for(;c!==null;)H=c,gf(c),c=c.sibling;H=l,js=E,He=R}yf(e)}else(l.subtreeFlags&8772)!==0&&c!==null?(c.return=l,H=c):yf(e)}}function yf(e){for(;H!==null;){var t=H;if((t.flags&8772)!==0){var i=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:He||Ns(5,t);break;case 1:var o=t.stateNode;if(t.flags&4&&!He)if(i===null)o.componentDidMount();else{var l=t.elementType===t.type?i.memoizedProps:Nt(t.type,i.memoizedProps);o.componentDidUpdate(l,i.memoizedState,o.__reactInternalSnapshotBeforeUpdate)}var c=t.updateQueue;c!==null&&yd(t,c,o);break;case 3:var m=t.updateQueue;if(m!==null){if(i=null,t.child!==null)switch(t.child.tag){case 5:i=t.child.stateNode;break;case 1:i=t.child.stateNode}yd(t,m,i)}break;case 5:var E=t.stateNode;if(i===null&&t.flags&4){i=E;var T=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":T.autoFocus&&i.focus();break;case"img":T.src&&(i.src=T.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var R=t.alternate;if(R!==null){var b=R.memoizedState;if(b!==null){var O=b.dehydrated;O!==null&&zr(O)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(s(163))}He||t.flags&512&&Ya(t)}catch(V){je(t,t.return,V)}}if(t===e){H=null;break}if(i=t.sibling,i!==null){i.return=t.return,H=i;break}H=t.return}}function vf(e){for(;H!==null;){var t=H;if(t===e){H=null;break}var i=t.sibling;if(i!==null){i.return=t.return,H=i;break}H=t.return}}function xf(e){for(;H!==null;){var t=H;try{switch(t.tag){case 0:case 11:case 15:var i=t.return;try{Ns(4,t)}catch(T){je(t,i,T)}break;case 1:var o=t.stateNode;if(typeof o.componentDidMount=="function"){var l=t.return;try{o.componentDidMount()}catch(T){je(t,l,T)}}var c=t.return;try{Ya(t)}catch(T){je(t,c,T)}break;case 5:var m=t.return;try{Ya(t)}catch(T){je(t,m,T)}}}catch(T){je(t,t.return,T)}if(t===e){H=null;break}var E=t.sibling;if(E!==null){E.return=t.return,H=E;break}H=t.return}}var Xg=Math.ceil,Rs=Y.ReactCurrentDispatcher,qa=Y.ReactCurrentOwner,xt=Y.ReactCurrentBatchConfig,ue=0,Oe=null,Le=null,Be=0,pt=0,mr=an(0),_e=0,ci=null,Vn=0,Ms=0,Ja=0,di=null,rt=null,el=0,gr=1/0,Gt=null,As=!1,tl=null,hn=null,Ls=!1,pn=null,Ds=0,fi=0,nl=null,Vs=-1,_s=0;function Xe(){return(ue&6)!==0?Re():Vs!==-1?Vs:Vs=Re()}function mn(e){return(e.mode&1)===0?1:(ue&2)!==0&&Be!==0?Be&-Be:Dg.transition!==null?(_s===0&&(_s=dc()),_s):(e=pe,e!==0||(e=window.event,e=e===void 0?16:wc(e.type)),e)}function At(e,t,i,o){if(50<fi)throw fi=0,nl=null,Error(s(185));br(e,i,o),((ue&2)===0||e!==Oe)&&(e===Oe&&((ue&2)===0&&(Ms|=i),_e===4&&gn(e,Be)),it(e,o),i===1&&ue===0&&(t.mode&1)===0&&(gr=Re()+500,us&&un()))}function it(e,t){var i=e.callbackNode;D0(e,t);var o=Wi(e,e===Oe?Be:0);if(o===0)i!==null&&lc(i),e.callbackNode=null,e.callbackPriority=0;else if(t=o&-o,e.callbackPriority!==t){if(i!=null&&lc(i),t===1)e.tag===0?Lg(Sf.bind(null,e)):sd(Sf.bind(null,e)),Ng(function(){(ue&6)===0&&un()}),i=null;else{switch(fc(o)){case 1:i=_o;break;case 4:i=uc;break;case 16:i=Bi;break;case 536870912:i=cc;break;default:i=Bi}i=Rf(i,wf.bind(null,e))}e.callbackPriority=t,e.callbackNode=i}}function wf(e,t){if(Vs=-1,_s=0,(ue&6)!==0)throw Error(s(327));var i=e.callbackNode;if(yr()&&e.callbackNode!==i)return null;var o=Wi(e,e===Oe?Be:0);if(o===0)return null;if((o&30)!==0||(o&e.expiredLanes)!==0||t)t=bs(e,o);else{t=o;var l=ue;ue|=2;var c=Cf();(Oe!==e||Be!==t)&&(Gt=null,gr=Re()+500,bn(e,t));do try{Jg();break}catch(E){Ef(e,E)}while(!0);wa(),Rs.current=c,ue=l,Le!==null?t=0:(Oe=null,Be=0,t=_e)}if(t!==0){if(t===2&&(l=bo(e),l!==0&&(o=l,t=rl(e,l))),t===1)throw i=ci,bn(e,0),gn(e,o),it(e,Re()),i;if(t===6)gn(e,o);else{if(l=e.current.alternate,(o&30)===0&&!Zg(l)&&(t=bs(e,o),t===2&&(c=bo(e),c!==0&&(o=c,t=rl(e,c))),t===1))throw i=ci,bn(e,0),gn(e,o),it(e,Re()),i;switch(e.finishedWork=l,e.finishedLanes=o,t){case 0:case 1:throw Error(s(345));case 2:On(e,rt,Gt);break;case 3:if(gn(e,o),(o&130023424)===o&&(t=el+500-Re(),10<t)){if(Wi(e,0)!==0)break;if(l=e.suspendedLanes,(l&o)!==o){Xe(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=ua(On.bind(null,e,rt,Gt),t);break}On(e,rt,Gt);break;case 4:if(gn(e,o),(o&4194240)===o)break;for(t=e.eventTimes,l=-1;0<o;){var m=31-kt(o);c=1<<m,m=t[m],m>l&&(l=m),o&=~c}if(o=l,o=Re()-o,o=(120>o?120:480>o?480:1080>o?1080:1920>o?1920:3e3>o?3e3:4320>o?4320:1960*Xg(o/1960))-o,10<o){e.timeoutHandle=ua(On.bind(null,e,rt,Gt),o);break}On(e,rt,Gt);break;case 5:On(e,rt,Gt);break;default:throw Error(s(329))}}}return it(e,Re()),e.callbackNode===i?wf.bind(null,e):null}function rl(e,t){var i=di;return e.current.memoizedState.isDehydrated&&(bn(e,t).flags|=256),e=bs(e,t),e!==2&&(t=rt,rt=i,t!==null&&il(t)),e}function il(e){rt===null?rt=e:rt.push.apply(rt,e)}function Zg(e){for(var t=e;;){if(t.flags&16384){var i=t.updateQueue;if(i!==null&&(i=i.stores,i!==null))for(var o=0;o<i.length;o++){var l=i[o],c=l.getSnapshot;l=l.value;try{if(!Pt(c(),l))return!1}catch{return!1}}}if(i=t.child,t.subtreeFlags&16384&&i!==null)i.return=t,t=i;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function gn(e,t){for(t&=~Ja,t&=~Ms,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var i=31-kt(t),o=1<<i;e[i]=-1,t&=~o}}function Sf(e){if((ue&6)!==0)throw Error(s(327));yr();var t=Wi(e,0);if((t&1)===0)return it(e,Re()),null;var i=bs(e,t);if(e.tag!==0&&i===2){var o=bo(e);o!==0&&(t=o,i=rl(e,o))}if(i===1)throw i=ci,bn(e,0),gn(e,t),it(e,Re()),i;if(i===6)throw Error(s(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,On(e,rt,Gt),it(e,Re()),null}function sl(e,t){var i=ue;ue|=1;try{return e(t)}finally{ue=i,ue===0&&(gr=Re()+500,us&&un())}}function _n(e){pn!==null&&pn.tag===0&&(ue&6)===0&&yr();var t=ue;ue|=1;var i=xt.transition,o=pe;try{if(xt.transition=null,pe=1,e)return e()}finally{pe=o,xt.transition=i,ue=t,(ue&6)===0&&un()}}function ol(){pt=mr.current,xe(mr)}function bn(e,t){e.finishedWork=null,e.finishedLanes=0;var i=e.timeoutHandle;if(i!==-1&&(e.timeoutHandle=-1,jg(i)),Le!==null)for(i=Le.return;i!==null;){var o=i;switch(ma(o),o.tag){case 1:o=o.type.childContextTypes,o!=null&&as();break;case 3:fr(),xe(et),xe(Ue),Na();break;case 5:Pa(o);break;case 4:fr();break;case 13:xe(Ce);break;case 19:xe(Ce);break;case 10:Sa(o.type._context);break;case 22:case 23:ol()}i=i.return}if(Oe=e,Le=e=yn(e.current,null),Be=pt=t,_e=0,ci=null,Ja=Ms=Vn=0,rt=di=null,An!==null){for(t=0;t<An.length;t++)if(i=An[t],o=i.interleaved,o!==null){i.interleaved=null;var l=o.next,c=i.pending;if(c!==null){var m=c.next;c.next=l,o.next=m}i.pending=o}An=null}return e}function Ef(e,t){do{var i=Le;try{if(wa(),xs.current=Cs,ws){for(var o=Te.memoizedState;o!==null;){var l=o.queue;l!==null&&(l.pending=null),o=o.next}ws=!1}if(Dn=0,be=Ve=Te=null,ii=!1,si=0,qa.current=null,i===null||i.return===null){_e=1,ci=t,Le=null;break}e:{var c=e,m=i.return,E=i,T=t;if(t=Be,E.flags|=32768,T!==null&&typeof T=="object"&&typeof T.then=="function"){var R=T,b=E,O=b.tag;if((b.mode&1)===0&&(O===0||O===11||O===15)){var V=b.alternate;V?(b.updateQueue=V.updateQueue,b.memoizedState=V.memoizedState,b.lanes=V.lanes):(b.updateQueue=null,b.memoizedState=null)}var $=Kd(m);if($!==null){$.flags&=-257,Qd($,m,E,c,t),$.mode&1&&Hd(c,R,t),t=$,T=R;var K=t.updateQueue;if(K===null){var Q=new Set;Q.add(T),t.updateQueue=Q}else K.add(T);break e}else{if((t&1)===0){Hd(c,R,t),al();break e}T=Error(s(426))}}else if(Ee&&E.mode&1){var Me=Kd(m);if(Me!==null){(Me.flags&65536)===0&&(Me.flags|=256),Qd(Me,m,E,c,t),va(hr(T,E));break e}}c=T=hr(T,E),_e!==4&&(_e=2),di===null?di=[c]:di.push(c),c=m;do{switch(c.tag){case 3:c.flags|=65536,t&=-t,c.lanes|=t;var j=$d(c,T,t);gd(c,j);break e;case 1:E=T;var k=c.type,N=c.stateNode;if((c.flags&128)===0&&(typeof k.getDerivedStateFromError=="function"||N!==null&&typeof N.componentDidCatch=="function"&&(hn===null||!hn.has(N)))){c.flags|=65536,t&=-t,c.lanes|=t;var I=Wd(c,E,t);gd(c,I);break e}}c=c.return}while(c!==null)}kf(i)}catch(G){t=G,Le===i&&i!==null&&(Le=i=i.return);continue}break}while(!0)}function Cf(){var e=Rs.current;return Rs.current=Cs,e===null?Cs:e}function al(){(_e===0||_e===3||_e===2)&&(_e=4),Oe===null||(Vn&268435455)===0&&(Ms&268435455)===0||gn(Oe,Be)}function bs(e,t){var i=ue;ue|=2;var o=Cf();(Oe!==e||Be!==t)&&(Gt=null,bn(e,t));do try{qg();break}catch(l){Ef(e,l)}while(!0);if(wa(),ue=i,Rs.current=o,Le!==null)throw Error(s(261));return Oe=null,Be=0,_e}function qg(){for(;Le!==null;)Tf(Le)}function Jg(){for(;Le!==null&&!T0();)Tf(Le)}function Tf(e){var t=Nf(e.alternate,e,pt);e.memoizedProps=e.pendingProps,t===null?kf(e):Le=t,qa.current=null}function kf(e){var t=e;do{var i=t.alternate;if(e=t.return,(t.flags&32768)===0){if(i=Hg(i,t,pt),i!==null){Le=i;return}}else{if(i=Kg(i,t),i!==null){i.flags&=32767,Le=i;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{_e=6,Le=null;return}}if(t=t.sibling,t!==null){Le=t;return}Le=t=e}while(t!==null);_e===0&&(_e=5)}function On(e,t,i){var o=pe,l=xt.transition;try{xt.transition=null,pe=1,ey(e,t,i,o)}finally{xt.transition=l,pe=o}return null}function ey(e,t,i,o){do yr();while(pn!==null);if((ue&6)!==0)throw Error(s(327));i=e.finishedWork;var l=e.finishedLanes;if(i===null)return null;if(e.finishedWork=null,e.finishedLanes=0,i===e.current)throw Error(s(177));e.callbackNode=null,e.callbackPriority=0;var c=i.lanes|i.childLanes;if(V0(e,c),e===Oe&&(Le=Oe=null,Be=0),(i.subtreeFlags&2064)===0&&(i.flags&2064)===0||Ls||(Ls=!0,Rf(Bi,function(){return yr(),null})),c=(i.flags&15990)!==0,(i.subtreeFlags&15990)!==0||c){c=xt.transition,xt.transition=null;var m=pe;pe=1;var E=ue;ue|=4,qa.current=null,Gg(e,i),mf(i,e),wg(aa),Qi=!!oa,aa=oa=null,e.current=i,Yg(i),k0(),ue=E,pe=m,xt.transition=c}else e.current=i;if(Ls&&(Ls=!1,pn=e,Ds=l),c=e.pendingLanes,c===0&&(hn=null),N0(i.stateNode),it(e,Re()),t!==null)for(o=e.onRecoverableError,i=0;i<t.length;i++)l=t[i],o(l.value,{componentStack:l.stack,digest:l.digest});if(As)throw As=!1,e=tl,tl=null,e;return(Ds&1)!==0&&e.tag!==0&&yr(),c=e.pendingLanes,(c&1)!==0?e===nl?fi++:(fi=0,nl=e):fi=0,un(),null}function yr(){if(pn!==null){var e=fc(Ds),t=xt.transition,i=pe;try{if(xt.transition=null,pe=16>e?16:e,pn===null)var o=!1;else{if(e=pn,pn=null,Ds=0,(ue&6)!==0)throw Error(s(331));var l=ue;for(ue|=4,H=e.current;H!==null;){var c=H,m=c.child;if((H.flags&16)!==0){var E=c.deletions;if(E!==null){for(var T=0;T<E.length;T++){var R=E[T];for(H=R;H!==null;){var b=H;switch(b.tag){case 0:case 11:case 15:ui(8,b,c)}var O=b.child;if(O!==null)O.return=b,H=O;else for(;H!==null;){b=H;var V=b.sibling,$=b.return;if(cf(b),b===R){H=null;break}if(V!==null){V.return=$,H=V;break}H=$}}}var K=c.alternate;if(K!==null){var Q=K.child;if(Q!==null){K.child=null;do{var Me=Q.sibling;Q.sibling=null,Q=Me}while(Q!==null)}}H=c}}if((c.subtreeFlags&2064)!==0&&m!==null)m.return=c,H=m;else e:for(;H!==null;){if(c=H,(c.flags&2048)!==0)switch(c.tag){case 0:case 11:case 15:ui(9,c,c.return)}var j=c.sibling;if(j!==null){j.return=c.return,H=j;break e}H=c.return}}var k=e.current;for(H=k;H!==null;){m=H;var N=m.child;if((m.subtreeFlags&2064)!==0&&N!==null)N.return=m,H=N;else e:for(m=k;H!==null;){if(E=H,(E.flags&2048)!==0)try{switch(E.tag){case 0:case 11:case 15:Ns(9,E)}}catch(G){je(E,E.return,G)}if(E===m){H=null;break e}var I=E.sibling;if(I!==null){I.return=E.return,H=I;break e}H=E.return}}if(ue=l,un(),Dt&&typeof Dt.onPostCommitFiberRoot=="function")try{Dt.onPostCommitFiberRoot(zi,e)}catch{}o=!0}return o}finally{pe=i,xt.transition=t}}return!1}function Pf(e,t,i){t=hr(i,t),t=$d(e,t,1),e=dn(e,t,1),t=Xe(),e!==null&&(br(e,1,t),it(e,t))}function je(e,t,i){if(e.tag===3)Pf(e,e,i);else for(;t!==null;){if(t.tag===3){Pf(t,e,i);break}else if(t.tag===1){var o=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof o.componentDidCatch=="function"&&(hn===null||!hn.has(o))){e=hr(i,e),e=Wd(t,e,1),t=dn(t,e,1),e=Xe(),t!==null&&(br(t,1,e),it(t,e));break}}t=t.return}}function ty(e,t,i){var o=e.pingCache;o!==null&&o.delete(t),t=Xe(),e.pingedLanes|=e.suspendedLanes&i,Oe===e&&(Be&i)===i&&(_e===4||_e===3&&(Be&130023424)===Be&&500>Re()-el?bn(e,0):Ja|=i),it(e,t)}function jf(e,t){t===0&&((e.mode&1)===0?t=1:(t=$i,$i<<=1,($i&130023424)===0&&($i=4194304)));var i=Xe();e=Ht(e,t),e!==null&&(br(e,t,i),it(e,i))}function ny(e){var t=e.memoizedState,i=0;t!==null&&(i=t.retryLane),jf(e,i)}function ry(e,t){var i=0;switch(e.tag){case 13:var o=e.stateNode,l=e.memoizedState;l!==null&&(i=l.retryLane);break;case 19:o=e.stateNode;break;default:throw Error(s(314))}o!==null&&o.delete(t),jf(e,i)}var Nf;Nf=function(e,t,i){if(e!==null)if(e.memoizedProps!==t.pendingProps||et.current)nt=!0;else{if((e.lanes&i)===0&&(t.flags&128)===0)return nt=!1,Wg(e,t,i);nt=(e.flags&131072)!==0}else nt=!1,Ee&&(t.flags&1048576)!==0&&od(t,ds,t.index);switch(t.lanes=0,t.tag){case 2:var o=t.type;Ps(e,t),e=t.pendingProps;var l=sr(t,Ue.current);dr(t,i),l=Aa(null,t,o,e,l,i);var c=La();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,tt(o)?(c=!0,ls(t)):c=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,Ta(t),l.updater=Ts,t.stateNode=l,l._reactInternals=t,Fa(t,o,e,i),t=Ua(null,t,o,!0,c,i)):(t.tag=0,Ee&&c&&pa(t),Ye(null,t,l,i),t=t.child),t;case 16:o=t.elementType;e:{switch(Ps(e,t),e=t.pendingProps,l=o._init,o=l(o._payload),t.type=o,l=t.tag=sy(o),e=Nt(o,e),l){case 0:t=za(null,t,o,e,i);break e;case 1:t=Jd(null,t,o,e,i);break e;case 11:t=Gd(null,t,o,e,i);break e;case 14:t=Yd(null,t,o,Nt(o.type,e),i);break e}throw Error(s(306,o,""))}return t;case 0:return o=t.type,l=t.pendingProps,l=t.elementType===o?l:Nt(o,l),za(e,t,o,l,i);case 1:return o=t.type,l=t.pendingProps,l=t.elementType===o?l:Nt(o,l),Jd(e,t,o,l,i);case 3:e:{if(ef(t),e===null)throw Error(s(387));o=t.pendingProps,c=t.memoizedState,l=c.element,md(e,t),ys(t,o,null,i);var m=t.memoizedState;if(o=m.element,c.isDehydrated)if(c={element:o,isDehydrated:!1,cache:m.cache,pendingSuspenseBoundaries:m.pendingSuspenseBoundaries,transitions:m.transitions},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){l=hr(Error(s(423)),t),t=tf(e,t,o,i,l);break e}else if(o!==l){l=hr(Error(s(424)),t),t=tf(e,t,o,i,l);break e}else for(ht=on(t.stateNode.containerInfo.firstChild),ft=t,Ee=!0,jt=null,i=hd(t,null,o,i),t.child=i;i;)i.flags=i.flags&-3|4096,i=i.sibling;else{if(lr(),o===l){t=Qt(e,t,i);break e}Ye(e,t,o,i)}t=t.child}return t;case 5:return vd(t),e===null&&ya(t),o=t.type,l=t.pendingProps,c=e!==null?e.memoizedProps:null,m=l.children,la(o,l)?m=null:c!==null&&la(o,c)&&(t.flags|=32),qd(e,t),Ye(e,t,m,i),t.child;case 6:return e===null&&ya(t),null;case 13:return nf(e,t,i);case 4:return ka(t,t.stateNode.containerInfo),o=t.pendingProps,e===null?t.child=ur(t,null,o,i):Ye(e,t,o,i),t.child;case 11:return o=t.type,l=t.pendingProps,l=t.elementType===o?l:Nt(o,l),Gd(e,t,o,l,i);case 7:return Ye(e,t,t.pendingProps,i),t.child;case 8:return Ye(e,t,t.pendingProps.children,i),t.child;case 12:return Ye(e,t,t.pendingProps.children,i),t.child;case 10:e:{if(o=t.type._context,l=t.pendingProps,c=t.memoizedProps,m=l.value,ye(ps,o._currentValue),o._currentValue=m,c!==null)if(Pt(c.value,m)){if(c.children===l.children&&!et.current){t=Qt(e,t,i);break e}}else for(c=t.child,c!==null&&(c.return=t);c!==null;){var E=c.dependencies;if(E!==null){m=c.child;for(var T=E.firstContext;T!==null;){if(T.context===o){if(c.tag===1){T=Kt(-1,i&-i),T.tag=2;var R=c.updateQueue;if(R!==null){R=R.shared;var b=R.pending;b===null?T.next=T:(T.next=b.next,b.next=T),R.pending=T}}c.lanes|=i,T=c.alternate,T!==null&&(T.lanes|=i),Ea(c.return,i,t),E.lanes|=i;break}T=T.next}}else if(c.tag===10)m=c.type===t.type?null:c.child;else if(c.tag===18){if(m=c.return,m===null)throw Error(s(341));m.lanes|=i,E=m.alternate,E!==null&&(E.lanes|=i),Ea(m,i,t),m=c.sibling}else m=c.child;if(m!==null)m.return=c;else for(m=c;m!==null;){if(m===t){m=null;break}if(c=m.sibling,c!==null){c.return=m.return,m=c;break}m=m.return}c=m}Ye(e,t,l.children,i),t=t.child}return t;case 9:return l=t.type,o=t.pendingProps.children,dr(t,i),l=yt(l),o=o(l),t.flags|=1,Ye(e,t,o,i),t.child;case 14:return o=t.type,l=Nt(o,t.pendingProps),l=Nt(o.type,l),Yd(e,t,o,l,i);case 15:return Xd(e,t,t.type,t.pendingProps,i);case 17:return o=t.type,l=t.pendingProps,l=t.elementType===o?l:Nt(o,l),Ps(e,t),t.tag=1,tt(o)?(e=!0,ls(t)):e=!1,dr(t,i),zd(t,o,l),Fa(t,o,l,i),Ua(null,t,o,!0,e,i);case 19:return sf(e,t,i);case 22:return Zd(e,t,i)}throw Error(s(156,t.tag))};function Rf(e,t){return ac(e,t)}function iy(e,t,i,o){this.tag=e,this.key=i,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=o,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function wt(e,t,i,o){return new iy(e,t,i,o)}function ll(e){return e=e.prototype,!(!e||!e.isReactComponent)}function sy(e){if(typeof e=="function")return ll(e)?1:0;if(e!=null){if(e=e.$$typeof,e===ut)return 11;if(e===Tt)return 14}return 2}function yn(e,t){var i=e.alternate;return i===null?(i=wt(e.tag,t,e.key,e.mode),i.elementType=e.elementType,i.type=e.type,i.stateNode=e.stateNode,i.alternate=e,e.alternate=i):(i.pendingProps=t,i.type=e.type,i.flags=0,i.subtreeFlags=0,i.deletions=null),i.flags=e.flags&14680064,i.childLanes=e.childLanes,i.lanes=e.lanes,i.child=e.child,i.memoizedProps=e.memoizedProps,i.memoizedState=e.memoizedState,i.updateQueue=e.updateQueue,t=e.dependencies,i.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},i.sibling=e.sibling,i.index=e.index,i.ref=e.ref,i}function Os(e,t,i,o,l,c){var m=2;if(o=e,typeof e=="function")ll(e)&&(m=1);else if(typeof e=="string")m=5;else e:switch(e){case re:return Fn(i.children,l,c,t);case Z:m=8,l|=8;break;case me:return e=wt(12,i,t,l|2),e.elementType=me,e.lanes=c,e;case Qe:return e=wt(13,i,t,l),e.elementType=Qe,e.lanes=c,e;case Je:return e=wt(19,i,t,l),e.elementType=Je,e.lanes=c,e;case se:return Fs(i,l,c,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case we:m=10;break e;case qe:m=9;break e;case ut:m=11;break e;case Tt:m=14;break e;case Ge:m=16,o=null;break e}throw Error(s(130,e==null?e:typeof e,""))}return t=wt(m,i,t,l),t.elementType=e,t.type=o,t.lanes=c,t}function Fn(e,t,i,o){return e=wt(7,e,o,t),e.lanes=i,e}function Fs(e,t,i,o){return e=wt(22,e,o,t),e.elementType=se,e.lanes=i,e.stateNode={isHidden:!1},e}function ul(e,t,i){return e=wt(6,e,null,t),e.lanes=i,e}function cl(e,t,i){return t=wt(4,e.children!==null?e.children:[],e.key,t),t.lanes=i,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function oy(e,t,i,o,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Oo(0),this.expirationTimes=Oo(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Oo(0),this.identifierPrefix=o,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function dl(e,t,i,o,l,c,m,E,T){return e=new oy(e,t,i,E,T),t===1?(t=1,c===!0&&(t|=8)):t=0,c=wt(3,null,null,t),e.current=c,c.stateNode=e,c.memoizedState={element:o,isDehydrated:i,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ta(c),e}function ay(e,t,i){var o=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:X,key:o==null?null:""+o,children:e,containerInfo:t,implementation:i}}function Mf(e){if(!e)return ln;e=e._reactInternals;e:{if(Pn(e)!==e||e.tag!==1)throw Error(s(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(tt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(s(171))}if(e.tag===1){var i=e.type;if(tt(i))return rd(e,i,t)}return t}function Af(e,t,i,o,l,c,m,E,T){return e=dl(i,o,!0,e,l,c,m,E,T),e.context=Mf(null),i=e.current,o=Xe(),l=mn(i),c=Kt(o,l),c.callback=t??null,dn(i,c,l),e.current.lanes=l,br(e,l,o),it(e,o),e}function Is(e,t,i,o){var l=t.current,c=Xe(),m=mn(l);return i=Mf(i),t.context===null?t.context=i:t.pendingContext=i,t=Kt(c,m),t.payload={element:e},o=o===void 0?null:o,o!==null&&(t.callback=o),e=dn(l,t,m),e!==null&&(At(e,l,m,c),gs(e,l,m)),m}function Bs(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Lf(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var i=e.retryLane;e.retryLane=i!==0&&i<t?i:t}}function fl(e,t){Lf(e,t),(e=e.alternate)&&Lf(e,t)}function ly(){return null}var Df=typeof reportError=="function"?reportError:function(e){console.error(e)};function hl(e){this._internalRoot=e}zs.prototype.render=hl.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(s(409));Is(e,t,null,null)},zs.prototype.unmount=hl.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;_n(function(){Is(null,e,null,null)}),t[zt]=null}};function zs(e){this._internalRoot=e}zs.prototype.unstable_scheduleHydration=function(e){if(e){var t=mc();e={blockedOn:null,target:e,priority:t};for(var i=0;i<nn.length&&t!==0&&t<nn[i].priority;i++);nn.splice(i,0,e),i===0&&vc(e)}};function pl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Us(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Vf(){}function uy(e,t,i,o,l){if(l){if(typeof o=="function"){var c=o;o=function(){var R=Bs(m);c.call(R)}}var m=Af(t,o,e,0,null,!1,!1,"",Vf);return e._reactRootContainer=m,e[zt]=m.current,Xr(e.nodeType===8?e.parentNode:e),_n(),m}for(;l=e.lastChild;)e.removeChild(l);if(typeof o=="function"){var E=o;o=function(){var R=Bs(T);E.call(R)}}var T=dl(e,0,!1,null,null,!1,!1,"",Vf);return e._reactRootContainer=T,e[zt]=T.current,Xr(e.nodeType===8?e.parentNode:e),_n(function(){Is(t,T,i,o)}),T}function $s(e,t,i,o,l){var c=i._reactRootContainer;if(c){var m=c;if(typeof l=="function"){var E=l;l=function(){var T=Bs(m);E.call(T)}}Is(t,m,e,l)}else m=uy(i,t,e,l,o);return Bs(m)}hc=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var i=_r(t.pendingLanes);i!==0&&(Fo(t,i|1),it(t,Re()),(ue&6)===0&&(gr=Re()+500,un()))}break;case 13:_n(function(){var o=Ht(e,1);if(o!==null){var l=Xe();At(o,e,1,l)}}),fl(e,1)}},Io=function(e){if(e.tag===13){var t=Ht(e,134217728);if(t!==null){var i=Xe();At(t,e,134217728,i)}fl(e,134217728)}},pc=function(e){if(e.tag===13){var t=mn(e),i=Ht(e,t);if(i!==null){var o=Xe();At(i,e,t,o)}fl(e,t)}},mc=function(){return pe},gc=function(e,t){var i=pe;try{return pe=e,t()}finally{pe=i}},Ao=function(e,t,i){switch(t){case"input":if(Co(e,i),t=i.name,i.type==="radio"&&t!=null){for(i=e;i.parentNode;)i=i.parentNode;for(i=i.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<i.length;t++){var o=i[t];if(o!==e&&o.form===e.form){var l=os(o);if(!l)throw Error(s(90));Bu(o),Co(o,l)}}}break;case"textarea":Hu(e,i);break;case"select":t=i.value,t!=null&&Qn(e,!!i.multiple,t,!1)}},ec=sl,tc=_n;var cy={usingClientEntryPoint:!1,Events:[Jr,rr,os,qu,Ju,sl]},hi={findFiberByHostInstance:jn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},dy={bundleType:hi.bundleType,version:hi.version,rendererPackageName:hi.rendererPackageName,rendererConfig:hi.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Y.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=sc(e),e===null?null:e.stateNode},findFiberByHostInstance:hi.findFiberByHostInstance||ly,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ws=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ws.isDisabled&&Ws.supportsFiber)try{zi=Ws.inject(dy),Dt=Ws}catch{}}return st.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=cy,st.createPortal=function(e,t){var i=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!pl(t))throw Error(s(200));return ay(e,t,null,i)},st.createRoot=function(e,t){if(!pl(e))throw Error(s(299));var i=!1,o="",l=Df;return t!=null&&(t.unstable_strictMode===!0&&(i=!0),t.identifierPrefix!==void 0&&(o=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=dl(e,1,!1,null,null,i,!1,o,l),e[zt]=t.current,Xr(e.nodeType===8?e.parentNode:e),new hl(t)},st.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(s(188)):(e=Object.keys(e).join(","),Error(s(268,e)));return e=sc(t),e=e===null?null:e.stateNode,e},st.flushSync=function(e){return _n(e)},st.hydrate=function(e,t,i){if(!Us(t))throw Error(s(200));return $s(null,e,t,!0,i)},st.hydrateRoot=function(e,t,i){if(!pl(e))throw Error(s(405));var o=i!=null&&i.hydratedSources||null,l=!1,c="",m=Df;if(i!=null&&(i.unstable_strictMode===!0&&(l=!0),i.identifierPrefix!==void 0&&(c=i.identifierPrefix),i.onRecoverableError!==void 0&&(m=i.onRecoverableError)),t=Af(t,null,e,1,i??null,l,!1,c,m),e[zt]=t.current,Xr(e),o)for(e=0;e<o.length;e++)i=o[e],l=i._getVersion,l=l(i._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[i,l]:t.mutableSourceEagerHydrationData.push(i,l);return new zs(t)},st.render=function(e,t,i){if(!Us(t))throw Error(s(200));return $s(null,e,t,!1,i)},st.unmountComponentAtNode=function(e){if(!Us(e))throw Error(s(40));return e._reactRootContainer?(_n(function(){$s(null,null,e,!1,function(){e._reactRootContainer=null,e[zt]=null})}),!0):!1},st.unstable_batchedUpdates=sl,st.unstable_renderSubtreeIntoContainer=function(e,t,i,o){if(!Us(i))throw Error(s(200));if(e==null||e._reactInternals===void 0)throw Error(s(38));return $s(e,t,i,!1,o)},st.version="18.3.1-next-f1338f8080-20240426",st}var Uf;function Ep(){if(Uf)return yl.exports;Uf=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),yl.exports=Sy(),yl.exports}var $f;function Ey(){if($f)return Hs;$f=1;var n=Ep();return Hs.createRoot=n.createRoot,Hs.hydrateRoot=n.hydrateRoot,Hs}var Cy=Ey();const Ty=n=>n instanceof Error?n.message+`
`+n.stack:JSON.stringify(n,null,2);class Cp extends Sp.Component{constructor(r){super(r),this.state={hasError:!1,error:null}}static getDerivedStateFromError(r){return{hasError:!0,error:r}}render(){return this.state.hasError?v.jsxs("div",{className:"p-4 border border-red-500 rounded",children:[v.jsx("h2",{className:"text-red-500",children:"Something went wrong."}),v.jsx("pre",{className:"mt-2 text-sm",children:Ty(this.state.error)})]}):this.props.children}}Ep();/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function io(){return io=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var s=arguments[r];for(var a in s)Object.prototype.hasOwnProperty.call(s,a)&&(n[a]=s[a])}return n},io.apply(this,arguments)}var Sn;(function(n){n.Pop="POP",n.Push="PUSH",n.Replace="REPLACE"})(Sn||(Sn={}));const Wf="popstate";function ky(n){n===void 0&&(n={});function r(a,u){let{pathname:d,search:f,hash:h}=a.location;return Ll("",{pathname:d,search:f,hash:h},u.state&&u.state.usr||null,u.state&&u.state.key||"default")}function s(a,u){return typeof u=="string"?u:kp(u)}return jy(r,s,null,n)}function lt(n,r){if(n===!1||n===null||typeof n>"u")throw new Error(r)}function Tp(n,r){if(!n){typeof console<"u"&&console.warn(r);try{throw new Error(r)}catch{}}}function Py(){return Math.random().toString(36).substr(2,8)}function Hf(n,r){return{usr:n.state,key:n.key,idx:r}}function Ll(n,r,s,a){return s===void 0&&(s=null),io({pathname:typeof n=="string"?n:n.pathname,search:"",hash:""},typeof r=="string"?ho(r):r,{state:s,key:r&&r.key||a||Py()})}function kp(n){let{pathname:r="/",search:s="",hash:a=""}=n;return s&&s!=="?"&&(r+=s.charAt(0)==="?"?s:"?"+s),a&&a!=="#"&&(r+=a.charAt(0)==="#"?a:"#"+a),r}function ho(n){let r={};if(n){let s=n.indexOf("#");s>=0&&(r.hash=n.substr(s),n=n.substr(0,s));let a=n.indexOf("?");a>=0&&(r.search=n.substr(a),n=n.substr(0,a)),n&&(r.pathname=n)}return r}function jy(n,r,s,a){a===void 0&&(a={});let{window:u=document.defaultView,v5Compat:d=!1}=a,f=u.history,h=Sn.Pop,p=null,g=y();g==null&&(g=0,f.replaceState(io({},f.state,{idx:g}),""));function y(){return(f.state||{idx:null}).idx}function x(){h=Sn.Pop;let M=y(),L=M==null?null:M-g;g=M,p&&p({action:h,location:D.location,delta:L})}function w(M,L){h=Sn.Push;let z=Ll(D.location,M,L);g=y()+1;let F=Hf(z,g),Y=D.createHref(z);try{f.pushState(F,"",Y)}catch(U){if(U instanceof DOMException&&U.name==="DataCloneError")throw U;u.location.assign(Y)}d&&p&&p({action:h,location:D.location,delta:1})}function S(M,L){h=Sn.Replace;let z=Ll(D.location,M,L);g=y();let F=Hf(z,g),Y=D.createHref(z);f.replaceState(F,"",Y),d&&p&&p({action:h,location:D.location,delta:0})}function A(M){let L=u.location.origin!=="null"?u.location.origin:u.location.href,z=typeof M=="string"?M:kp(M);return z=z.replace(/ $/,"%20"),lt(L,"No window.location.(origin|href) available to create URL for href: "+z),new URL(z,L)}let D={get action(){return h},get location(){return n(u,f)},listen(M){if(p)throw new Error("A history only accepts one active listener");return u.addEventListener(Wf,x),p=M,()=>{u.removeEventListener(Wf,x),p=null}},createHref(M){return r(u,M)},createURL:A,encodeLocation(M){let L=A(M);return{pathname:L.pathname,search:L.search,hash:L.hash}},push:w,replace:S,go(M){return f.go(M)}};return D}var Kf;(function(n){n.data="data",n.deferred="deferred",n.redirect="redirect",n.error="error"})(Kf||(Kf={}));function Ny(n,r,s){return s===void 0&&(s="/"),Ry(n,r,s)}function Ry(n,r,s,a){let u=typeof r=="string"?ho(r):r,d=Np(u.pathname||"/",s);if(d==null)return null;let f=Pp(n);My(f);let h=null;for(let p=0;h==null&&p<f.length;++p){let g=Uy(d);h=Iy(f[p],g)}return h}function Pp(n,r,s,a){r===void 0&&(r=[]),s===void 0&&(s=[]),a===void 0&&(a="");let u=(d,f,h)=>{let p={relativePath:h===void 0?d.path||"":h,caseSensitive:d.caseSensitive===!0,childrenIndex:f,route:d};p.relativePath.startsWith("/")&&(lt(p.relativePath.startsWith(a),'Absolute route path "'+p.relativePath+'" nested under path '+('"'+a+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),p.relativePath=p.relativePath.slice(a.length));let g=Cr([a,p.relativePath]),y=s.concat(p);d.children&&d.children.length>0&&(lt(d.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+g+'".')),Pp(d.children,r,y,g)),!(d.path==null&&!d.index)&&r.push({path:g,score:Oy(g,d.index),routesMeta:y})};return n.forEach((d,f)=>{var h;if(d.path===""||!((h=d.path)!=null&&h.includes("?")))u(d,f);else for(let p of jp(d.path))u(d,f,p)}),r}function jp(n){let r=n.split("/");if(r.length===0)return[];let[s,...a]=r,u=s.endsWith("?"),d=s.replace(/\?$/,"");if(a.length===0)return u?[d,""]:[d];let f=jp(a.join("/")),h=[];return h.push(...f.map(p=>p===""?d:[d,p].join("/"))),u&&h.push(...f),h.map(p=>n.startsWith("/")&&p===""?"/":p)}function My(n){n.sort((r,s)=>r.score!==s.score?s.score-r.score:Fy(r.routesMeta.map(a=>a.childrenIndex),s.routesMeta.map(a=>a.childrenIndex)))}const Ay=/^:[\w-]+$/,Ly=3,Dy=2,Vy=1,_y=10,by=-2,Qf=n=>n==="*";function Oy(n,r){let s=n.split("/"),a=s.length;return s.some(Qf)&&(a+=by),r&&(a+=Dy),s.filter(u=>!Qf(u)).reduce((u,d)=>u+(Ay.test(d)?Ly:d===""?Vy:_y),a)}function Fy(n,r){return n.length===r.length&&n.slice(0,-1).every((a,u)=>a===r[u])?n[n.length-1]-r[r.length-1]:0}function Iy(n,r,s){let{routesMeta:a}=n,u={},d="/",f=[];for(let h=0;h<a.length;++h){let p=a[h],g=h===a.length-1,y=d==="/"?r:r.slice(d.length)||"/",x=By({path:p.relativePath,caseSensitive:p.caseSensitive,end:g},y),w=p.route;if(!x)return null;Object.assign(u,x.params),f.push({params:u,pathname:Cr([d,x.pathname]),pathnameBase:$y(Cr([d,x.pathnameBase])),route:w}),x.pathnameBase!=="/"&&(d=Cr([d,x.pathnameBase]))}return f}function By(n,r){typeof n=="string"&&(n={path:n,caseSensitive:!1,end:!0});let[s,a]=zy(n.path,n.caseSensitive,n.end),u=r.match(s);if(!u)return null;let d=u[0],f=d.replace(/(.)\/+$/,"$1"),h=u.slice(1);return{params:a.reduce((g,y,x)=>{let{paramName:w,isOptional:S}=y;if(w==="*"){let D=h[x]||"";f=d.slice(0,d.length-D.length).replace(/(.)\/+$/,"$1")}const A=h[x];return S&&!A?g[w]=void 0:g[w]=(A||"").replace(/%2F/g,"/"),g},{}),pathname:d,pathnameBase:f,pattern:n}}function zy(n,r,s){r===void 0&&(r=!1),s===void 0&&(s=!0),Tp(n==="*"||!n.endsWith("*")||n.endsWith("/*"),'Route path "'+n+'" will be treated as if it were '+('"'+n.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+n.replace(/\*$/,"/*")+'".'));let a=[],u="^"+n.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(f,h,p)=>(a.push({paramName:h,isOptional:p!=null}),p?"/?([^\\/]+)?":"/([^\\/]+)"));return n.endsWith("*")?(a.push({paramName:"*"}),u+=n==="*"||n==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):s?u+="\\/*$":n!==""&&n!=="/"&&(u+="(?:(?=\\/|$))"),[new RegExp(u,r?void 0:"i"),a]}function Uy(n){try{return n.split("/").map(r=>decodeURIComponent(r).replace(/\//g,"%2F")).join("/")}catch(r){return Tp(!1,'The URL path "'+n+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+r+").")),n}}function Np(n,r){if(r==="/")return n;if(!n.toLowerCase().startsWith(r.toLowerCase()))return null;let s=r.endsWith("/")?r.length-1:r.length,a=n.charAt(s);return a&&a!=="/"?null:n.slice(s)||"/"}const Cr=n=>n.join("/").replace(/\/\/+/g,"/"),$y=n=>n.replace(/\/+$/,"").replace(/^\/*/,"/");function Wy(n){return n!=null&&typeof n.status=="number"&&typeof n.statusText=="string"&&typeof n.internal=="boolean"&&"data"in n}const Rp=["post","put","patch","delete"];new Set(Rp);const Hy=["get",...Rp];new Set(Hy);/**
 * React Router v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function so(){return so=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var s=arguments[r];for(var a in s)Object.prototype.hasOwnProperty.call(s,a)&&(n[a]=s[a])}return n},so.apply(this,arguments)}const Ky=C.createContext(null),Qy=C.createContext(null),Mp=C.createContext(null),po=C.createContext(null),mo=C.createContext({outlet:null,matches:[],isDataRoute:!1}),Ap=C.createContext(null);function iu(){return C.useContext(po)!=null}function Gy(){return iu()||lt(!1),C.useContext(po).location}function Yy(n,r){return Xy(n,r)}function Xy(n,r,s,a){iu()||lt(!1);let{navigator:u,static:d}=C.useContext(Mp),{matches:f}=C.useContext(mo),h=f[f.length-1],p=h?h.params:{};h&&h.pathname;let g=h?h.pathnameBase:"/";h&&h.route;let y=Gy(),x;if(r){var w;let L=typeof r=="string"?ho(r):r;g==="/"||(w=L.pathname)!=null&&w.startsWith(g)||lt(!1),x=L}else x=y;let S=x.pathname||"/",A=S;if(g!=="/"){let L=g.replace(/^\//,"").split("/");A="/"+S.replace(/^\//,"").split("/").slice(L.length).join("/")}let D=Ny(n,{pathname:A}),M=tv(D&&D.map(L=>Object.assign({},L,{params:Object.assign({},p,L.params),pathname:Cr([g,u.encodeLocation?u.encodeLocation(L.pathname).pathname:L.pathname]),pathnameBase:L.pathnameBase==="/"?g:Cr([g,u.encodeLocation?u.encodeLocation(L.pathnameBase).pathname:L.pathnameBase])})),f,s,a);return r&&M?C.createElement(po.Provider,{value:{location:so({pathname:"/",search:"",hash:"",state:null,key:"default"},x),navigationType:Sn.Pop}},M):M}function Zy(){let n=sv(),r=Wy(n)?n.status+" "+n.statusText:n instanceof Error?n.message:JSON.stringify(n),s=n instanceof Error?n.stack:null,u={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return C.createElement(C.Fragment,null,C.createElement("h2",null,"Unexpected Application Error!"),C.createElement("h3",{style:{fontStyle:"italic"}},r),s?C.createElement("pre",{style:u},s):null,null)}const qy=C.createElement(Zy,null);class Jy extends C.Component{constructor(r){super(r),this.state={location:r.location,revalidation:r.revalidation,error:r.error}}static getDerivedStateFromError(r){return{error:r}}static getDerivedStateFromProps(r,s){return s.location!==r.location||s.revalidation!=="idle"&&r.revalidation==="idle"?{error:r.error,location:r.location,revalidation:r.revalidation}:{error:r.error!==void 0?r.error:s.error,location:s.location,revalidation:r.revalidation||s.revalidation}}componentDidCatch(r,s){console.error("React Router caught the following error during render",r,s)}render(){return this.state.error!==void 0?C.createElement(mo.Provider,{value:this.props.routeContext},C.createElement(Ap.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function ev(n){let{routeContext:r,match:s,children:a}=n,u=C.useContext(Ky);return u&&u.static&&u.staticContext&&(s.route.errorElement||s.route.ErrorBoundary)&&(u.staticContext._deepestRenderedBoundaryId=s.route.id),C.createElement(mo.Provider,{value:r},a)}function tv(n,r,s,a){var u;if(r===void 0&&(r=[]),s===void 0&&(s=null),a===void 0&&(a=null),n==null){var d;if(!s)return null;if(s.errors)n=s.matches;else if((d=a)!=null&&d.v7_partialHydration&&r.length===0&&!s.initialized&&s.matches.length>0)n=s.matches;else return null}let f=n,h=(u=s)==null?void 0:u.errors;if(h!=null){let y=f.findIndex(x=>x.route.id&&(h==null?void 0:h[x.route.id])!==void 0);y>=0||lt(!1),f=f.slice(0,Math.min(f.length,y+1))}let p=!1,g=-1;if(s&&a&&a.v7_partialHydration)for(let y=0;y<f.length;y++){let x=f[y];if((x.route.HydrateFallback||x.route.hydrateFallbackElement)&&(g=y),x.route.id){let{loaderData:w,errors:S}=s,A=x.route.loader&&w[x.route.id]===void 0&&(!S||S[x.route.id]===void 0);if(x.route.lazy||A){p=!0,g>=0?f=f.slice(0,g+1):f=[f[0]];break}}}return f.reduceRight((y,x,w)=>{let S,A=!1,D=null,M=null;s&&(S=h&&x.route.id?h[x.route.id]:void 0,D=x.route.errorElement||qy,p&&(g<0&&w===0?(ov("route-fallback"),A=!0,M=null):g===w&&(A=!0,M=x.route.hydrateFallbackElement||null)));let L=r.concat(f.slice(0,w+1)),z=()=>{let F;return S?F=D:A?F=M:x.route.Component?F=C.createElement(x.route.Component,null):x.route.element?F=x.route.element:F=y,C.createElement(ev,{match:x,routeContext:{outlet:y,matches:L,isDataRoute:s!=null},children:F})};return s&&(x.route.ErrorBoundary||x.route.errorElement||w===0)?C.createElement(Jy,{location:s.location,revalidation:s.revalidation,component:D,error:S,children:z(),routeContext:{outlet:null,matches:L,isDataRoute:!0}}):z()},null)}var Lp=function(n){return n.UseBlocker="useBlocker",n.UseLoaderData="useLoaderData",n.UseActionData="useActionData",n.UseRouteError="useRouteError",n.UseNavigation="useNavigation",n.UseRouteLoaderData="useRouteLoaderData",n.UseMatches="useMatches",n.UseRevalidator="useRevalidator",n.UseNavigateStable="useNavigate",n.UseRouteId="useRouteId",n}(Lp||{});function nv(n){let r=C.useContext(Qy);return r||lt(!1),r}function rv(n){let r=C.useContext(mo);return r||lt(!1),r}function iv(n){let r=rv(),s=r.matches[r.matches.length-1];return s.route.id||lt(!1),s.route.id}function sv(){var n;let r=C.useContext(Ap),s=nv(Lp.UseRouteError),a=iv();return r!==void 0?r:(n=s.errors)==null?void 0:n[a]}const Gf={};function ov(n,r,s){Gf[n]||(Gf[n]=!0)}function av(n,r){n==null||n.v7_startTransition,n==null||n.v7_relativeSplatPath}function Zs(n){lt(!1)}function lv(n){let{basename:r="/",children:s=null,location:a,navigationType:u=Sn.Pop,navigator:d,static:f=!1,future:h}=n;iu()&&lt(!1);let p=r.replace(/^\/*/,"/"),g=C.useMemo(()=>({basename:p,navigator:d,static:f,future:so({v7_relativeSplatPath:!1},h)}),[p,h,d,f]);typeof a=="string"&&(a=ho(a));let{pathname:y="/",search:x="",hash:w="",state:S=null,key:A="default"}=a,D=C.useMemo(()=>{let M=Np(y,p);return M==null?null:{location:{pathname:M,search:x,hash:w,state:S,key:A},navigationType:u}},[p,y,x,w,S,A,u]);return D==null?null:C.createElement(Mp.Provider,{value:g},C.createElement(po.Provider,{children:s,value:D}))}function uv(n){let{children:r,location:s}=n;return Yy(Dl(r),s)}new Promise(()=>{});function Dl(n,r){r===void 0&&(r=[]);let s=[];return C.Children.forEach(n,(a,u)=>{if(!C.isValidElement(a))return;let d=[...r,u];if(a.type===C.Fragment){s.push.apply(s,Dl(a.props.children,d));return}a.type!==Zs&&lt(!1),!a.props.index||!a.props.children||lt(!1);let f={id:a.props.id||d.join("-"),caseSensitive:a.props.caseSensitive,element:a.props.element,Component:a.props.Component,index:a.props.index,path:a.props.path,loader:a.props.loader,action:a.props.action,errorElement:a.props.errorElement,ErrorBoundary:a.props.ErrorBoundary,hasErrorBoundary:a.props.ErrorBoundary!=null||a.props.errorElement!=null,shouldRevalidate:a.props.shouldRevalidate,handle:a.props.handle,lazy:a.props.lazy};a.props.children&&(f.children=Dl(a.props.children,d)),s.push(f)}),s}/**
 * React Router DOM v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const cv="6";try{window.__reactRouterVersion=cv}catch{}const dv="startTransition",Yf=vy[dv];function fv(n){let{basename:r,children:s,future:a,window:u}=n,d=C.useRef();d.current==null&&(d.current=ky({window:u,v5Compat:!0}));let f=d.current,[h,p]=C.useState({action:f.action,location:f.location}),{v7_startTransition:g}=a||{},y=C.useCallback(x=>{g&&Yf?Yf(()=>p(x)):p(x)},[p,g]);return C.useLayoutEffect(()=>f.listen(y),[f,y]),C.useEffect(()=>av(a),[a]),C.createElement(lv,{basename:r,children:s,location:h.location,navigationType:h.action,navigator:f,future:a})}var Xf;(function(n){n.UseScrollRestoration="useScrollRestoration",n.UseSubmit="useSubmit",n.UseSubmitFetcher="useSubmitFetcher",n.UseFetcher="useFetcher",n.useViewTransitionState="useViewTransitionState"})(Xf||(Xf={}));var Zf;(function(n){n.UseFetcher="useFetcher",n.UseFetchers="useFetchers",n.UseScrollRestoration="useScrollRestoration"})(Zf||(Zf={}));let hv={data:""},pv=n=>typeof window=="object"?((n?n.querySelector("#_goober"):window._goober)||Object.assign((n||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:n||hv,mv=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,gv=/\/\*[^]*?\*\/|  +/g,qf=/\n+/g,wn=(n,r)=>{let s="",a="",u="";for(let d in n){let f=n[d];d[0]=="@"?d[1]=="i"?s=d+" "+f+";":a+=d[1]=="f"?wn(f,d):d+"{"+wn(f,d[1]=="k"?"":r)+"}":typeof f=="object"?a+=wn(f,r?r.replace(/([^,])+/g,h=>d.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,p=>/&/.test(p)?p.replace(/&/g,h):h?h+" "+p:p)):d):f!=null&&(d=/^--/.test(d)?d:d.replace(/[A-Z]/g,"-$&").toLowerCase(),u+=wn.p?wn.p(d,f):d+":"+f+";")}return s+(r&&u?r+"{"+u+"}":u)+a},Yt={},Dp=n=>{if(typeof n=="object"){let r="";for(let s in n)r+=s+Dp(n[s]);return r}return n},yv=(n,r,s,a,u)=>{let d=Dp(n),f=Yt[d]||(Yt[d]=(p=>{let g=0,y=11;for(;g<p.length;)y=101*y+p.charCodeAt(g++)>>>0;return"go"+y})(d));if(!Yt[f]){let p=d!==n?n:(g=>{let y,x,w=[{}];for(;y=mv.exec(g.replace(gv,""));)y[4]?w.shift():y[3]?(x=y[3].replace(qf," ").trim(),w.unshift(w[0][x]=w[0][x]||{})):w[0][y[1]]=y[2].replace(qf," ").trim();return w[0]})(n);Yt[f]=wn(u?{["@keyframes "+f]:p}:p,s?"":"."+f)}let h=s&&Yt.g?Yt.g:null;return s&&(Yt.g=Yt[f]),((p,g,y,x)=>{x?g.data=g.data.replace(x,p):g.data.indexOf(p)===-1&&(g.data=y?p+g.data:g.data+p)})(Yt[f],r,a,h),f},vv=(n,r,s)=>n.reduce((a,u,d)=>{let f=r[d];if(f&&f.call){let h=f(s),p=h&&h.props&&h.props.className||/^go/.test(h)&&h;f=p?"."+p:h&&typeof h=="object"?h.props?"":wn(h,""):h===!1?"":h}return a+u+(f??"")},"");function go(n){let r=this||{},s=n.call?n(r.p):n;return yv(s.unshift?s.raw?vv(s,[].slice.call(arguments,1),r.p):s.reduce((a,u)=>Object.assign(a,u&&u.call?u(r.p):u),{}):s,pv(r.target),r.g,r.o,r.k)}let Vp,Vl,_l;go.bind({g:1});let Xt=go.bind({k:1});function xv(n,r,s,a){wn.p=r,Vp=n,Vl=s,_l=a}function Tn(n,r){let s=this||{};return function(){let a=arguments;function u(d,f){let h=Object.assign({},d),p=h.className||u.className;s.p=Object.assign({theme:Vl&&Vl()},h),s.o=/ *go\d+/.test(p),h.className=go.apply(s,a)+(p?" "+p:"");let g=n;return n[0]&&(g=h.as||n,delete h.as),_l&&g[0]&&_l(h),Vp(g,h)}return u}}var wv=n=>typeof n=="function",oo=(n,r)=>wv(n)?n(r):n,Sv=(()=>{let n=0;return()=>(++n).toString()})(),_p=(()=>{let n;return()=>{if(n===void 0&&typeof window<"u"){let r=matchMedia("(prefers-reduced-motion: reduce)");n=!r||r.matches}return n}})(),Ev=20,bp=(n,r)=>{switch(r.type){case 0:return{...n,toasts:[r.toast,...n.toasts].slice(0,Ev)};case 1:return{...n,toasts:n.toasts.map(d=>d.id===r.toast.id?{...d,...r.toast}:d)};case 2:let{toast:s}=r;return bp(n,{type:n.toasts.find(d=>d.id===s.id)?1:0,toast:s});case 3:let{toastId:a}=r;return{...n,toasts:n.toasts.map(d=>d.id===a||a===void 0?{...d,dismissed:!0,visible:!1}:d)};case 4:return r.toastId===void 0?{...n,toasts:[]}:{...n,toasts:n.toasts.filter(d=>d.id!==r.toastId)};case 5:return{...n,pausedAt:r.time};case 6:let u=r.time-(n.pausedAt||0);return{...n,pausedAt:void 0,toasts:n.toasts.map(d=>({...d,pauseDuration:d.pauseDuration+u}))}}},qs=[],zn={toasts:[],pausedAt:void 0},Kn=n=>{zn=bp(zn,n),qs.forEach(r=>{r(zn)})},Cv={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},Tv=(n={})=>{let[r,s]=C.useState(zn),a=C.useRef(zn);C.useEffect(()=>(a.current!==zn&&s(zn),qs.push(s),()=>{let d=qs.indexOf(s);d>-1&&qs.splice(d,1)}),[]);let u=r.toasts.map(d=>{var f,h,p;return{...n,...n[d.type],...d,removeDelay:d.removeDelay||((f=n[d.type])==null?void 0:f.removeDelay)||(n==null?void 0:n.removeDelay),duration:d.duration||((h=n[d.type])==null?void 0:h.duration)||(n==null?void 0:n.duration)||Cv[d.type],style:{...n.style,...(p=n[d.type])==null?void 0:p.style,...d.style}}});return{...r,toasts:u}},kv=(n,r="blank",s)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:r,ariaProps:{role:"status","aria-live":"polite"},message:n,pauseDuration:0,...s,id:(s==null?void 0:s.id)||Sv()}),Mi=n=>(r,s)=>{let a=kv(r,n,s);return Kn({type:2,toast:a}),a.id},ot=(n,r)=>Mi("blank")(n,r);ot.error=Mi("error");ot.success=Mi("success");ot.loading=Mi("loading");ot.custom=Mi("custom");ot.dismiss=n=>{Kn({type:3,toastId:n})};ot.remove=n=>Kn({type:4,toastId:n});ot.promise=(n,r,s)=>{let a=ot.loading(r.loading,{...s,...s==null?void 0:s.loading});return typeof n=="function"&&(n=n()),n.then(u=>{let d=r.success?oo(r.success,u):void 0;return d?ot.success(d,{id:a,...s,...s==null?void 0:s.success}):ot.dismiss(a),u}).catch(u=>{let d=r.error?oo(r.error,u):void 0;d?ot.error(d,{id:a,...s,...s==null?void 0:s.error}):ot.dismiss(a)}),n};var Pv=(n,r)=>{Kn({type:1,toast:{id:n,height:r}})},jv=()=>{Kn({type:5,time:Date.now()})},yi=new Map,Nv=1e3,Rv=(n,r=Nv)=>{if(yi.has(n))return;let s=setTimeout(()=>{yi.delete(n),Kn({type:4,toastId:n})},r);yi.set(n,s)},Mv=n=>{let{toasts:r,pausedAt:s}=Tv(n);C.useEffect(()=>{if(s)return;let d=Date.now(),f=r.map(h=>{if(h.duration===1/0)return;let p=(h.duration||0)+h.pauseDuration-(d-h.createdAt);if(p<0){h.visible&&ot.dismiss(h.id);return}return setTimeout(()=>ot.dismiss(h.id),p)});return()=>{f.forEach(h=>h&&clearTimeout(h))}},[r,s]);let a=C.useCallback(()=>{s&&Kn({type:6,time:Date.now()})},[s]),u=C.useCallback((d,f)=>{let{reverseOrder:h=!1,gutter:p=8,defaultPosition:g}=f||{},y=r.filter(S=>(S.position||g)===(d.position||g)&&S.height),x=y.findIndex(S=>S.id===d.id),w=y.filter((S,A)=>A<x&&S.visible).length;return y.filter(S=>S.visible).slice(...h?[w+1]:[0,w]).reduce((S,A)=>S+(A.height||0)+p,0)},[r]);return C.useEffect(()=>{r.forEach(d=>{if(d.dismissed)Rv(d.id,d.removeDelay);else{let f=yi.get(d.id);f&&(clearTimeout(f),yi.delete(d.id))}})},[r]),{toasts:r,handlers:{updateHeight:Pv,startPause:jv,endPause:a,calculateOffset:u}}},Av=Xt`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,Lv=Xt`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Dv=Xt`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,Vv=Tn("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${n=>n.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Av} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Lv} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${n=>n.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Dv} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,_v=Xt`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,bv=Tn("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${n=>n.secondary||"#e0e0e0"};
  border-right-color: ${n=>n.primary||"#616161"};
  animation: ${_v} 1s linear infinite;
`,Ov=Xt`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,Fv=Xt`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Iv=Tn("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${n=>n.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Ov} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${Fv} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${n=>n.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,Bv=Tn("div")`
  position: absolute;
`,zv=Tn("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,Uv=Xt`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,$v=Tn("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${Uv} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,Wv=({toast:n})=>{let{icon:r,type:s,iconTheme:a}=n;return r!==void 0?typeof r=="string"?C.createElement($v,null,r):r:s==="blank"?null:C.createElement(zv,null,C.createElement(bv,{...a}),s!=="loading"&&C.createElement(Bv,null,s==="error"?C.createElement(Vv,{...a}):C.createElement(Iv,{...a})))},Hv=n=>`
0% {transform: translate3d(0,${n*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,Kv=n=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${n*-150}%,-1px) scale(.6); opacity:0;}
`,Qv="0%{opacity:0;} 100%{opacity:1;}",Gv="0%{opacity:1;} 100%{opacity:0;}",Yv=Tn("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,Xv=Tn("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,Zv=(n,r)=>{let s=n.includes("top")?1:-1,[a,u]=_p()?[Qv,Gv]:[Hv(s),Kv(s)];return{animation:r?`${Xt(a)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${Xt(u)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},qv=C.memo(({toast:n,position:r,style:s,children:a})=>{let u=n.height?Zv(n.position||r||"top-center",n.visible):{opacity:0},d=C.createElement(Wv,{toast:n}),f=C.createElement(Xv,{...n.ariaProps},oo(n.message,n));return C.createElement(Yv,{className:n.className,style:{...u,...s,...n.style}},typeof a=="function"?a({icon:d,message:f}):C.createElement(C.Fragment,null,d,f))});xv(C.createElement);var Jv=({id:n,className:r,style:s,onHeightUpdate:a,children:u})=>{let d=C.useCallback(f=>{if(f){let h=()=>{let p=f.getBoundingClientRect().height;a(n,p)};h(),new MutationObserver(h).observe(f,{subtree:!0,childList:!0,characterData:!0})}},[n,a]);return C.createElement("div",{ref:d,className:r,style:s},u)},e1=(n,r)=>{let s=n.includes("top"),a=s?{top:0}:{bottom:0},u=n.includes("center")?{justifyContent:"center"}:n.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:_p()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${r*(s?1:-1)}px)`,...a,...u}},t1=go`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,Ks=16,n1=({reverseOrder:n,position:r="top-center",toastOptions:s,gutter:a,children:u,containerStyle:d,containerClassName:f})=>{let{toasts:h,handlers:p}=Mv(s);return C.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:Ks,left:Ks,right:Ks,bottom:Ks,pointerEvents:"none",...d},className:f,onMouseEnter:p.startPause,onMouseLeave:p.endPause},h.map(g=>{let y=g.position||r,x=p.calculateOffset(g,{reverseOrder:n,gutter:a,defaultPosition:r}),w=e1(y,x);return C.createElement(Jv,{id:g.id,key:g.id,onHeightUpdate:p.updateHeight,className:g.visible?t1:"",style:w},g.type==="custom"?oo(g.message,g):u?u(g):C.createElement(qv,{toast:g,position:y}))}))};const r1={user:null,userRole:"customer",vendors:[],products:[],orders:[],cart:[],config:null,isLoading:!1,error:null,selectedVendor:null,searchQuery:"",selectedCategory:"",userLocation:null},i1=(n,r)=>{switch(r.type){case"SET_USER":return{...n,user:r.payload};case"SET_USER_ROLE":return{...n,userRole:r.payload};case"SET_VENDORS":return{...n,vendors:r.payload};case"SET_PRODUCTS":return{...n,products:r.payload};case"SET_ORDERS":return{...n,orders:r.payload};case"SET_CONFIG":return{...n,config:r.payload};case"SET_LOADING":return{...n,isLoading:r.payload};case"SET_ERROR":return{...n,error:r.payload};case"ADD_TO_CART":return n.cart.find(a=>a.product.id===r.payload.product.id)?{...n,cart:n.cart.map(a=>a.product.id===r.payload.product.id?{...a,quantity:a.quantity+r.payload.quantity}:a)}:{...n,cart:[...n.cart,r.payload]};case"REMOVE_FROM_CART":return{...n,cart:n.cart.filter(a=>a.product.id!==r.payload)};case"UPDATE_CART_QUANTITY":return{...n,cart:n.cart.map(a=>a.product.id===r.payload.productId?{...a,quantity:r.payload.quantity}:a)};case"CLEAR_CART":return{...n,cart:[]};case"SET_SELECTED_VENDOR":return{...n,selectedVendor:r.payload};case"SET_SEARCH_QUERY":return{...n,searchQuery:r.payload};case"SET_SELECTED_CATEGORY":return{...n,selectedCategory:r.payload};case"SET_USER_LOCATION":return{...n,userLocation:r.payload};default:return n}},Op=C.createContext(void 0),yo=()=>{const n=C.useContext(Op);if(!n)throw new Error("useApp must be used within an AppProvider");return n},s1=({children:n})=>{const[r,s]=C.useReducer(i1,r1);C.useEffect(()=>{(async()=>{s({type:"SET_LOADING",payload:!0});try{const z=await(await fetch("/data/config.json")).json();s({type:"SET_CONFIG",payload:z});const Y=await(await fetch("/data/vendors.json")).json();s({type:"SET_VENDORS",payload:Y});const X=await(await fetch("/data/products.json")).json();s({type:"SET_PRODUCTS",payload:X});const Z=await(await fetch("/data/orders.json")).json();s({type:"SET_ORDERS",payload:Z}),s({type:"SET_USER_LOCATION",payload:{lat:14.6928,lng:-17.4467}})}catch{s({type:"SET_ERROR",payload:"Erreur lors du chargement des données"})}finally{s({type:"SET_LOADING",payload:!1})}})()},[]);const a=async(M,L)=>{s({type:"SET_LOADING",payload:!0});try{const Y=(await(await fetch("/data/users.json")).json()).find(U=>U.email===M);Y?(s({type:"SET_USER",payload:Y}),s({type:"SET_ERROR",payload:null})):s({type:"SET_ERROR",payload:"Utilisateur non trouvé"})}catch{s({type:"SET_ERROR",payload:"Erreur de connexion"})}finally{s({type:"SET_LOADING",payload:!1})}},u=()=>{s({type:"SET_USER",payload:null}),s({type:"CLEAR_CART"})},d=M=>{s({type:"SET_USER_ROLE",payload:M})},f=(M,L,z)=>{s({type:"ADD_TO_CART",payload:{product:M,vendor:L,quantity:z}})},h=M=>{s({type:"REMOVE_FROM_CART",payload:M})},D={state:r,dispatch:s,login:a,logout:u,switchRole:d,addToCart:f,removeFromCart:h,updateCartQuantity:(M,L)=>{L<=0?h(M):s({type:"UPDATE_CART_QUANTITY",payload:{productId:M,quantity:L}})},clearCart:()=>{s({type:"CLEAR_CART"})},getVendorById:M=>r.vendors.find(L=>L.id===M),getProductById:M=>r.products.find(L=>L.id===M),getProductsByVendor:M=>r.products.filter(L=>L.vendorId===M),getUserOrders:()=>r.orders.filter(M=>{var L;return M.userId===((L=r.user)==null?void 0:L.id)}),getVendorOrders:M=>r.orders.filter(L=>L.vendorId===M)};return v.jsx(Op.Provider,{value:D,children:n})},su=C.createContext({});function ou(n){const r=C.useRef(null);return r.current===null&&(r.current=n()),r.current}const au=typeof window<"u",Fp=au?C.useLayoutEffect:C.useEffect,vo=C.createContext(null);function lu(n,r){n.indexOf(r)===-1&&n.push(r)}function uu(n,r){const s=n.indexOf(r);s>-1&&n.splice(s,1)}const Zt=(n,r,s)=>s>r?r:s<n?n:s;let cu=()=>{};const qt={},Ip=n=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(n);function Bp(n){return typeof n=="object"&&n!==null}const zp=n=>/^0[^.\s]+$/u.test(n);function du(n){let r;return()=>(r===void 0&&(r=n()),r)}const Ct=n=>n,o1=(n,r)=>s=>r(n(s)),Ai=(...n)=>n.reduce(o1),Ci=(n,r,s)=>{const a=r-n;return a===0?1:(s-n)/a};class fu{constructor(){this.subscriptions=[]}add(r){return lu(this.subscriptions,r),()=>uu(this.subscriptions,r)}notify(r,s,a){const u=this.subscriptions.length;if(u)if(u===1)this.subscriptions[0](r,s,a);else for(let d=0;d<u;d++){const f=this.subscriptions[d];f&&f(r,s,a)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Ft=n=>n*1e3,It=n=>n/1e3;function Up(n,r){return r?n*(1e3/r):0}const $p=(n,r,s)=>(((1-3*s+3*r)*n+(3*s-6*r))*n+3*r)*n,a1=1e-7,l1=12;function u1(n,r,s,a,u){let d,f,h=0;do f=r+(s-r)/2,d=$p(f,a,u)-n,d>0?s=f:r=f;while(Math.abs(d)>a1&&++h<l1);return f}function Li(n,r,s,a){if(n===r&&s===a)return Ct;const u=d=>u1(d,0,1,n,s);return d=>d===0||d===1?d:$p(u(d),r,a)}const Wp=n=>r=>r<=.5?n(2*r)/2:(2-n(2*(1-r)))/2,Hp=n=>r=>1-n(1-r),Kp=Li(.33,1.53,.69,.99),hu=Hp(Kp),Qp=Wp(hu),Gp=n=>(n*=2)<1?.5*hu(n):.5*(2-Math.pow(2,-10*(n-1))),pu=n=>1-Math.sin(Math.acos(n)),Yp=Hp(pu),Xp=Wp(pu),c1=Li(.42,0,1,1),d1=Li(0,0,.58,1),Zp=Li(.42,0,.58,1),f1=n=>Array.isArray(n)&&typeof n[0]!="number",qp=n=>Array.isArray(n)&&typeof n[0]=="number",h1={linear:Ct,easeIn:c1,easeInOut:Zp,easeOut:d1,circIn:pu,circInOut:Xp,circOut:Yp,backIn:hu,backInOut:Qp,backOut:Kp,anticipate:Gp},p1=n=>typeof n=="string",Jf=n=>{if(qp(n)){cu(n.length===4);const[r,s,a,u]=n;return Li(r,s,a,u)}else if(p1(n))return h1[n];return n},Qs=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],eh={value:null};function m1(n,r){let s=new Set,a=new Set,u=!1,d=!1;const f=new WeakSet;let h={delta:0,timestamp:0,isProcessing:!1},p=0;function g(x){f.has(x)&&(y.schedule(x),n()),p++,x(h)}const y={schedule:(x,w=!1,S=!1)=>{const D=S&&u?s:a;return w&&f.add(x),D.has(x)||D.add(x),x},cancel:x=>{a.delete(x),f.delete(x)},process:x=>{if(h=x,u){d=!0;return}u=!0,[s,a]=[a,s],s.forEach(g),r&&eh.value&&eh.value.frameloop[r].push(p),p=0,s.clear(),u=!1,d&&(d=!1,y.process(x))}};return y}const g1=40;function Jp(n,r){let s=!1,a=!0;const u={delta:0,timestamp:0,isProcessing:!1},d=()=>s=!0,f=Qs.reduce((F,Y)=>(F[Y]=m1(d,r?Y:void 0),F),{}),{setup:h,read:p,resolveKeyframes:g,preUpdate:y,update:x,preRender:w,render:S,postRender:A}=f,D=()=>{const F=qt.useManualTiming?u.timestamp:performance.now();s=!1,qt.useManualTiming||(u.delta=a?1e3/60:Math.max(Math.min(F-u.timestamp,g1),1)),u.timestamp=F,u.isProcessing=!0,h.process(u),p.process(u),g.process(u),y.process(u),x.process(u),w.process(u),S.process(u),A.process(u),u.isProcessing=!1,s&&r&&(a=!1,n(D))},M=()=>{s=!0,a=!0,u.isProcessing||n(D)};return{schedule:Qs.reduce((F,Y)=>{const U=f[Y];return F[Y]=(X,re=!1,Z=!1)=>(s||M(),U.schedule(X,re,Z)),F},{}),cancel:F=>{for(let Y=0;Y<Qs.length;Y++)f[Qs[Y]].cancel(F)},state:u,steps:f}}const{schedule:Pe,cancel:En,state:ze,steps:wl}=Jp(typeof requestAnimationFrame<"u"?requestAnimationFrame:Ct,!0);let Js;function y1(){Js=void 0}const at={now:()=>(Js===void 0&&at.set(ze.isProcessing||qt.useManualTiming?ze.timestamp:performance.now()),Js),set:n=>{Js=n,queueMicrotask(y1)}},em=n=>r=>typeof r=="string"&&r.startsWith(n),mu=em("--"),v1=em("var(--"),gu=n=>v1(n)?x1.test(n.split("/*")[0].trim()):!1,x1=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Pr={test:n=>typeof n=="number",parse:parseFloat,transform:n=>n},Ti={...Pr,transform:n=>Zt(0,1,n)},Gs={...Pr,default:1},vi=n=>Math.round(n*1e5)/1e5,yu=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function w1(n){return n==null}const S1=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,vu=(n,r)=>s=>!!(typeof s=="string"&&S1.test(s)&&s.startsWith(n)||r&&!w1(s)&&Object.prototype.hasOwnProperty.call(s,r)),tm=(n,r,s)=>a=>{if(typeof a!="string")return a;const[u,d,f,h]=a.match(yu);return{[n]:parseFloat(u),[r]:parseFloat(d),[s]:parseFloat(f),alpha:h!==void 0?parseFloat(h):1}},E1=n=>Zt(0,255,n),Sl={...Pr,transform:n=>Math.round(E1(n))},Un={test:vu("rgb","red"),parse:tm("red","green","blue"),transform:({red:n,green:r,blue:s,alpha:a=1})=>"rgba("+Sl.transform(n)+", "+Sl.transform(r)+", "+Sl.transform(s)+", "+vi(Ti.transform(a))+")"};function C1(n){let r="",s="",a="",u="";return n.length>5?(r=n.substring(1,3),s=n.substring(3,5),a=n.substring(5,7),u=n.substring(7,9)):(r=n.substring(1,2),s=n.substring(2,3),a=n.substring(3,4),u=n.substring(4,5),r+=r,s+=s,a+=a,u+=u),{red:parseInt(r,16),green:parseInt(s,16),blue:parseInt(a,16),alpha:u?parseInt(u,16)/255:1}}const bl={test:vu("#"),parse:C1,transform:Un.transform},Di=n=>({test:r=>typeof r=="string"&&r.endsWith(n)&&r.split(" ").length===1,parse:parseFloat,transform:r=>`${r}${n}`}),xn=Di("deg"),Bt=Di("%"),te=Di("px"),T1=Di("vh"),k1=Di("vw"),th={...Bt,parse:n=>Bt.parse(n)/100,transform:n=>Bt.transform(n*100)},vr={test:vu("hsl","hue"),parse:tm("hue","saturation","lightness"),transform:({hue:n,saturation:r,lightness:s,alpha:a=1})=>"hsla("+Math.round(n)+", "+Bt.transform(vi(r))+", "+Bt.transform(vi(s))+", "+vi(Ti.transform(a))+")"},De={test:n=>Un.test(n)||bl.test(n)||vr.test(n),parse:n=>Un.test(n)?Un.parse(n):vr.test(n)?vr.parse(n):bl.parse(n),transform:n=>typeof n=="string"?n:n.hasOwnProperty("red")?Un.transform(n):vr.transform(n),getAnimatableNone:n=>{const r=De.parse(n);return r.alpha=0,De.transform(r)}},P1=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function j1(n){var r,s;return isNaN(n)&&typeof n=="string"&&(((r=n.match(yu))==null?void 0:r.length)||0)+(((s=n.match(P1))==null?void 0:s.length)||0)>0}const nm="number",rm="color",N1="var",R1="var(",nh="${}",M1=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ki(n){const r=n.toString(),s=[],a={color:[],number:[],var:[]},u=[];let d=0;const h=r.replace(M1,p=>(De.test(p)?(a.color.push(d),u.push(rm),s.push(De.parse(p))):p.startsWith(R1)?(a.var.push(d),u.push(N1),s.push(p)):(a.number.push(d),u.push(nm),s.push(parseFloat(p))),++d,nh)).split(nh);return{values:s,split:h,indexes:a,types:u}}function im(n){return ki(n).values}function sm(n){const{split:r,types:s}=ki(n),a=r.length;return u=>{let d="";for(let f=0;f<a;f++)if(d+=r[f],u[f]!==void 0){const h=s[f];h===nm?d+=vi(u[f]):h===rm?d+=De.transform(u[f]):d+=u[f]}return d}}const A1=n=>typeof n=="number"?0:De.test(n)?De.getAnimatableNone(n):n;function L1(n){const r=im(n);return sm(n)(r.map(A1))}const Cn={test:j1,parse:im,createTransformer:sm,getAnimatableNone:L1};function El(n,r,s){return s<0&&(s+=1),s>1&&(s-=1),s<1/6?n+(r-n)*6*s:s<1/2?r:s<2/3?n+(r-n)*(2/3-s)*6:n}function D1({hue:n,saturation:r,lightness:s,alpha:a}){n/=360,r/=100,s/=100;let u=0,d=0,f=0;if(!r)u=d=f=s;else{const h=s<.5?s*(1+r):s+r-s*r,p=2*s-h;u=El(p,h,n+1/3),d=El(p,h,n),f=El(p,h,n-1/3)}return{red:Math.round(u*255),green:Math.round(d*255),blue:Math.round(f*255),alpha:a}}function ao(n,r){return s=>s>0?r:n}const ke=(n,r,s)=>n+(r-n)*s,Cl=(n,r,s)=>{const a=n*n,u=s*(r*r-a)+a;return u<0?0:Math.sqrt(u)},V1=[bl,Un,vr],_1=n=>V1.find(r=>r.test(n));function rh(n){const r=_1(n);if(!r)return!1;let s=r.parse(n);return r===vr&&(s=D1(s)),s}const ih=(n,r)=>{const s=rh(n),a=rh(r);if(!s||!a)return ao(n,r);const u={...s};return d=>(u.red=Cl(s.red,a.red,d),u.green=Cl(s.green,a.green,d),u.blue=Cl(s.blue,a.blue,d),u.alpha=ke(s.alpha,a.alpha,d),Un.transform(u))},Ol=new Set(["none","hidden"]);function b1(n,r){return Ol.has(n)?s=>s<=0?n:r:s=>s>=1?r:n}function O1(n,r){return s=>ke(n,r,s)}function xu(n){return typeof n=="number"?O1:typeof n=="string"?gu(n)?ao:De.test(n)?ih:B1:Array.isArray(n)?om:typeof n=="object"?De.test(n)?ih:F1:ao}function om(n,r){const s=[...n],a=s.length,u=n.map((d,f)=>xu(d)(d,r[f]));return d=>{for(let f=0;f<a;f++)s[f]=u[f](d);return s}}function F1(n,r){const s={...n,...r},a={};for(const u in s)n[u]!==void 0&&r[u]!==void 0&&(a[u]=xu(n[u])(n[u],r[u]));return u=>{for(const d in a)s[d]=a[d](u);return s}}function I1(n,r){const s=[],a={color:0,var:0,number:0};for(let u=0;u<r.values.length;u++){const d=r.types[u],f=n.indexes[d][a[d]],h=n.values[f]??0;s[u]=h,a[d]++}return s}const B1=(n,r)=>{const s=Cn.createTransformer(r),a=ki(n),u=ki(r);return a.indexes.var.length===u.indexes.var.length&&a.indexes.color.length===u.indexes.color.length&&a.indexes.number.length>=u.indexes.number.length?Ol.has(n)&&!u.values.length||Ol.has(r)&&!a.values.length?b1(n,r):Ai(om(I1(a,u),u.values),s):ao(n,r)};function am(n,r,s){return typeof n=="number"&&typeof r=="number"&&typeof s=="number"?ke(n,r,s):xu(n)(n,r)}const z1=n=>{const r=({timestamp:s})=>n(s);return{start:(s=!0)=>Pe.update(r,s),stop:()=>En(r),now:()=>ze.isProcessing?ze.timestamp:at.now()}},lm=(n,r,s=10)=>{let a="";const u=Math.max(Math.round(r/s),2);for(let d=0;d<u;d++)a+=Math.round(n(d/(u-1))*1e4)/1e4+", ";return`linear(${a.substring(0,a.length-2)})`},lo=2e4;function wu(n){let r=0;const s=50;let a=n.next(r);for(;!a.done&&r<lo;)r+=s,a=n.next(r);return r>=lo?1/0:r}function U1(n,r=100,s){const a=s({...n,keyframes:[0,r]}),u=Math.min(wu(a),lo);return{type:"keyframes",ease:d=>a.next(u*d).value/r,duration:It(u)}}const $1=5;function um(n,r,s){const a=Math.max(r-$1,0);return Up(s-n(a),r-a)}const Ne={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},sh=.001;function W1({duration:n=Ne.duration,bounce:r=Ne.bounce,velocity:s=Ne.velocity,mass:a=Ne.mass}){let u,d,f=1-r;f=Zt(Ne.minDamping,Ne.maxDamping,f),n=Zt(Ne.minDuration,Ne.maxDuration,It(n)),f<1?(u=g=>{const y=g*f,x=y*n,w=y-s,S=Fl(g,f),A=Math.exp(-x);return sh-w/S*A},d=g=>{const x=g*f*n,w=x*s+s,S=Math.pow(f,2)*Math.pow(g,2)*n,A=Math.exp(-x),D=Fl(Math.pow(g,2),f);return(-u(g)+sh>0?-1:1)*((w-S)*A)/D}):(u=g=>{const y=Math.exp(-g*n),x=(g-s)*n+1;return-.001+y*x},d=g=>{const y=Math.exp(-g*n),x=(s-g)*(n*n);return y*x});const h=5/n,p=K1(u,d,h);if(n=Ft(n),isNaN(p))return{stiffness:Ne.stiffness,damping:Ne.damping,duration:n};{const g=Math.pow(p,2)*a;return{stiffness:g,damping:f*2*Math.sqrt(a*g),duration:n}}}const H1=12;function K1(n,r,s){let a=s;for(let u=1;u<H1;u++)a=a-n(a)/r(a);return a}function Fl(n,r){return n*Math.sqrt(1-r*r)}const Q1=["duration","bounce"],G1=["stiffness","damping","mass"];function oh(n,r){return r.some(s=>n[s]!==void 0)}function Y1(n){let r={velocity:Ne.velocity,stiffness:Ne.stiffness,damping:Ne.damping,mass:Ne.mass,isResolvedFromDuration:!1,...n};if(!oh(n,G1)&&oh(n,Q1))if(n.visualDuration){const s=n.visualDuration,a=2*Math.PI/(s*1.2),u=a*a,d=2*Zt(.05,1,1-(n.bounce||0))*Math.sqrt(u);r={...r,mass:Ne.mass,stiffness:u,damping:d}}else{const s=W1(n);r={...r,...s,mass:Ne.mass},r.isResolvedFromDuration=!0}return r}function uo(n=Ne.visualDuration,r=Ne.bounce){const s=typeof n!="object"?{visualDuration:n,keyframes:[0,1],bounce:r}:n;let{restSpeed:a,restDelta:u}=s;const d=s.keyframes[0],f=s.keyframes[s.keyframes.length-1],h={done:!1,value:d},{stiffness:p,damping:g,mass:y,duration:x,velocity:w,isResolvedFromDuration:S}=Y1({...s,velocity:-It(s.velocity||0)}),A=w||0,D=g/(2*Math.sqrt(p*y)),M=f-d,L=It(Math.sqrt(p/y)),z=Math.abs(M)<5;a||(a=z?Ne.restSpeed.granular:Ne.restSpeed.default),u||(u=z?Ne.restDelta.granular:Ne.restDelta.default);let F;if(D<1){const U=Fl(L,D);F=X=>{const re=Math.exp(-D*L*X);return f-re*((A+D*L*M)/U*Math.sin(U*X)+M*Math.cos(U*X))}}else if(D===1)F=U=>f-Math.exp(-L*U)*(M+(A+L*M)*U);else{const U=L*Math.sqrt(D*D-1);F=X=>{const re=Math.exp(-D*L*X),Z=Math.min(U*X,300);return f-re*((A+D*L*M)*Math.sinh(Z)+U*M*Math.cosh(Z))/U}}const Y={calculatedDuration:S&&x||null,next:U=>{const X=F(U);if(S)h.done=U>=x;else{let re=U===0?A:0;D<1&&(re=U===0?Ft(A):um(F,U,X));const Z=Math.abs(re)<=a,me=Math.abs(f-X)<=u;h.done=Z&&me}return h.value=h.done?f:X,h},toString:()=>{const U=Math.min(wu(Y),lo),X=lm(re=>Y.next(U*re).value,U,30);return U+"ms "+X},toTransition:()=>{}};return Y}uo.applyToOptions=n=>{const r=U1(n,100,uo);return n.ease=r.ease,n.duration=Ft(r.duration),n.type="keyframes",n};function Il({keyframes:n,velocity:r=0,power:s=.8,timeConstant:a=325,bounceDamping:u=10,bounceStiffness:d=500,modifyTarget:f,min:h,max:p,restDelta:g=.5,restSpeed:y}){const x=n[0],w={done:!1,value:x},S=Z=>h!==void 0&&Z<h||p!==void 0&&Z>p,A=Z=>h===void 0?p:p===void 0||Math.abs(h-Z)<Math.abs(p-Z)?h:p;let D=s*r;const M=x+D,L=f===void 0?M:f(M);L!==M&&(D=L-x);const z=Z=>-D*Math.exp(-Z/a),F=Z=>L+z(Z),Y=Z=>{const me=z(Z),we=F(Z);w.done=Math.abs(me)<=g,w.value=w.done?L:we};let U,X;const re=Z=>{S(w.value)&&(U=Z,X=uo({keyframes:[w.value,A(w.value)],velocity:um(F,Z,w.value),damping:u,stiffness:d,restDelta:g,restSpeed:y}))};return re(0),{calculatedDuration:null,next:Z=>{let me=!1;return!X&&U===void 0&&(me=!0,Y(Z),re(Z)),U!==void 0&&Z>=U?X.next(Z-U):(!me&&Y(Z),w)}}}function X1(n,r,s){const a=[],u=s||qt.mix||am,d=n.length-1;for(let f=0;f<d;f++){let h=u(n[f],n[f+1]);if(r){const p=Array.isArray(r)?r[f]||Ct:r;h=Ai(p,h)}a.push(h)}return a}function Z1(n,r,{clamp:s=!0,ease:a,mixer:u}={}){const d=n.length;if(cu(d===r.length),d===1)return()=>r[0];if(d===2&&r[0]===r[1])return()=>r[1];const f=n[0]===n[1];n[0]>n[d-1]&&(n=[...n].reverse(),r=[...r].reverse());const h=X1(r,a,u),p=h.length,g=y=>{if(f&&y<n[0])return r[0];let x=0;if(p>1)for(;x<n.length-2&&!(y<n[x+1]);x++);const w=Ci(n[x],n[x+1],y);return h[x](w)};return s?y=>g(Zt(n[0],n[d-1],y)):g}function q1(n,r){const s=n[n.length-1];for(let a=1;a<=r;a++){const u=Ci(0,r,a);n.push(ke(s,1,u))}}function J1(n){const r=[0];return q1(r,n.length-1),r}function ex(n,r){return n.map(s=>s*r)}function tx(n,r){return n.map(()=>r||Zp).splice(0,n.length-1)}function xi({duration:n=300,keyframes:r,times:s,ease:a="easeInOut"}){const u=f1(a)?a.map(Jf):Jf(a),d={done:!1,value:r[0]},f=ex(s&&s.length===r.length?s:J1(r),n),h=Z1(f,r,{ease:Array.isArray(u)?u:tx(r,u)});return{calculatedDuration:n,next:p=>(d.value=h(p),d.done=p>=n,d)}}const nx=n=>n!==null;function Su(n,{repeat:r,repeatType:s="loop"},a,u=1){const d=n.filter(nx),h=u<0||r&&s!=="loop"&&r%2===1?0:d.length-1;return!h||a===void 0?d[h]:a}const rx={decay:Il,inertia:Il,tween:xi,keyframes:xi,spring:uo};function cm(n){typeof n.type=="string"&&(n.type=rx[n.type])}class Eu{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(r=>{this.resolve=r})}notifyFinished(){this.resolve()}then(r,s){return this.finished.then(r,s)}}const ix=n=>n/100;class Cu extends Eu{constructor(r){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var a,u;const{motionValue:s}=this.options;s&&s.updatedAt!==at.now()&&this.tick(at.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),(u=(a=this.options).onStop)==null||u.call(a))},this.options=r,this.initAnimation(),this.play(),r.autoplay===!1&&this.pause()}initAnimation(){const{options:r}=this;cm(r);const{type:s=xi,repeat:a=0,repeatDelay:u=0,repeatType:d,velocity:f=0}=r;let{keyframes:h}=r;const p=s||xi;p!==xi&&typeof h[0]!="number"&&(this.mixKeyframes=Ai(ix,am(h[0],h[1])),h=[0,100]);const g=p({...r,keyframes:h});d==="mirror"&&(this.mirroredGenerator=p({...r,keyframes:[...h].reverse(),velocity:-f})),g.calculatedDuration===null&&(g.calculatedDuration=wu(g));const{calculatedDuration:y}=g;this.calculatedDuration=y,this.resolvedDuration=y+u,this.totalDuration=this.resolvedDuration*(a+1)-u,this.generator=g}updateTime(r){const s=Math.round(r-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=s}tick(r,s=!1){const{generator:a,totalDuration:u,mixKeyframes:d,mirroredGenerator:f,resolvedDuration:h,calculatedDuration:p}=this;if(this.startTime===null)return a.next(0);const{delay:g=0,keyframes:y,repeat:x,repeatType:w,repeatDelay:S,type:A,onUpdate:D,finalKeyframe:M}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,r):this.speed<0&&(this.startTime=Math.min(r-u/this.speed,this.startTime)),s?this.currentTime=r:this.updateTime(r);const L=this.currentTime-g*(this.playbackSpeed>=0?1:-1),z=this.playbackSpeed>=0?L<0:L>u;this.currentTime=Math.max(L,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=u);let F=this.currentTime,Y=a;if(x){const Z=Math.min(this.currentTime,u)/h;let me=Math.floor(Z),we=Z%1;!we&&Z>=1&&(we=1),we===1&&me--,me=Math.min(me,x+1),!!(me%2)&&(w==="reverse"?(we=1-we,S&&(we-=S/h)):w==="mirror"&&(Y=f)),F=Zt(0,1,we)*h}const U=z?{done:!1,value:y[0]}:Y.next(F);d&&(U.value=d(U.value));let{done:X}=U;!z&&p!==null&&(X=this.playbackSpeed>=0?this.currentTime>=u:this.currentTime<=0);const re=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&X);return re&&A!==Il&&(U.value=Su(y,this.options,M,this.speed)),D&&D(U.value),re&&this.finish(),U}then(r,s){return this.finished.then(r,s)}get duration(){return It(this.calculatedDuration)}get time(){return It(this.currentTime)}set time(r){var s;r=Ft(r),this.currentTime=r,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=r:this.driver&&(this.startTime=this.driver.now()-r/this.playbackSpeed),(s=this.driver)==null||s.start(!1)}get speed(){return this.playbackSpeed}set speed(r){this.updateTime(at.now());const s=this.playbackSpeed!==r;this.playbackSpeed=r,s&&(this.time=It(this.currentTime))}play(){var u,d;if(this.isStopped)return;const{driver:r=z1,startTime:s}=this.options;this.driver||(this.driver=r(f=>this.tick(f))),(d=(u=this.options).onPlay)==null||d.call(u);const a=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=a):this.holdTime!==null?this.startTime=a-this.holdTime:this.startTime||(this.startTime=s??a),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(at.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){var r,s;this.notifyFinished(),this.teardown(),this.state="finished",(s=(r=this.options).onComplete)==null||s.call(r)}cancel(){var r,s;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),(s=(r=this.options).onCancel)==null||s.call(r)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(r){return this.startTime=0,this.tick(r,!0)}attachTimeline(r){var s;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),(s=this.driver)==null||s.stop(),r.observe(this)}}function sx(n){for(let r=1;r<n.length;r++)n[r]??(n[r]=n[r-1])}const $n=n=>n*180/Math.PI,Bl=n=>{const r=$n(Math.atan2(n[1],n[0]));return zl(r)},ox={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:n=>(Math.abs(n[0])+Math.abs(n[3]))/2,rotate:Bl,rotateZ:Bl,skewX:n=>$n(Math.atan(n[1])),skewY:n=>$n(Math.atan(n[2])),skew:n=>(Math.abs(n[1])+Math.abs(n[2]))/2},zl=n=>(n=n%360,n<0&&(n+=360),n),ah=Bl,lh=n=>Math.sqrt(n[0]*n[0]+n[1]*n[1]),uh=n=>Math.sqrt(n[4]*n[4]+n[5]*n[5]),ax={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:lh,scaleY:uh,scale:n=>(lh(n)+uh(n))/2,rotateX:n=>zl($n(Math.atan2(n[6],n[5]))),rotateY:n=>zl($n(Math.atan2(-n[2],n[0]))),rotateZ:ah,rotate:ah,skewX:n=>$n(Math.atan(n[4])),skewY:n=>$n(Math.atan(n[1])),skew:n=>(Math.abs(n[1])+Math.abs(n[4]))/2};function Ul(n){return n.includes("scale")?1:0}function $l(n,r){if(!n||n==="none")return Ul(r);const s=n.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let a,u;if(s)a=ax,u=s;else{const h=n.match(/^matrix\(([-\d.e\s,]+)\)$/u);a=ox,u=h}if(!u)return Ul(r);const d=a[r],f=u[1].split(",").map(ux);return typeof d=="function"?d(f):f[d]}const lx=(n,r)=>{const{transform:s="none"}=getComputedStyle(n);return $l(s,r)};function ux(n){return parseFloat(n.trim())}const jr=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Nr=new Set(jr),ch=n=>n===Pr||n===te,cx=new Set(["x","y","z"]),dx=jr.filter(n=>!cx.has(n));function fx(n){const r=[];return dx.forEach(s=>{const a=n.getValue(s);a!==void 0&&(r.push([s,a.get()]),a.set(s.startsWith("scale")?1:0))}),r}const Wn={width:({x:n},{paddingLeft:r="0",paddingRight:s="0"})=>n.max-n.min-parseFloat(r)-parseFloat(s),height:({y:n},{paddingTop:r="0",paddingBottom:s="0"})=>n.max-n.min-parseFloat(r)-parseFloat(s),top:(n,{top:r})=>parseFloat(r),left:(n,{left:r})=>parseFloat(r),bottom:({y:n},{top:r})=>parseFloat(r)+(n.max-n.min),right:({x:n},{left:r})=>parseFloat(r)+(n.max-n.min),x:(n,{transform:r})=>$l(r,"x"),y:(n,{transform:r})=>$l(r,"y")};Wn.translateX=Wn.x;Wn.translateY=Wn.y;const Hn=new Set;let Wl=!1,Hl=!1,Kl=!1;function dm(){if(Hl){const n=Array.from(Hn).filter(a=>a.needsMeasurement),r=new Set(n.map(a=>a.element)),s=new Map;r.forEach(a=>{const u=fx(a);u.length&&(s.set(a,u),a.render())}),n.forEach(a=>a.measureInitialState()),r.forEach(a=>{a.render();const u=s.get(a);u&&u.forEach(([d,f])=>{var h;(h=a.getValue(d))==null||h.set(f)})}),n.forEach(a=>a.measureEndState()),n.forEach(a=>{a.suspendedScrollY!==void 0&&window.scrollTo(0,a.suspendedScrollY)})}Hl=!1,Wl=!1,Hn.forEach(n=>n.complete(Kl)),Hn.clear()}function fm(){Hn.forEach(n=>{n.readKeyframes(),n.needsMeasurement&&(Hl=!0)})}function hx(){Kl=!0,fm(),dm(),Kl=!1}class Tu{constructor(r,s,a,u,d,f=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...r],this.onComplete=s,this.name=a,this.motionValue=u,this.element=d,this.isAsync=f}scheduleResolve(){this.state="scheduled",this.isAsync?(Hn.add(this),Wl||(Wl=!0,Pe.read(fm),Pe.resolveKeyframes(dm))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:r,name:s,element:a,motionValue:u}=this;if(r[0]===null){const d=u==null?void 0:u.get(),f=r[r.length-1];if(d!==void 0)r[0]=d;else if(a&&s){const h=a.readValue(s,f);h!=null&&(r[0]=h)}r[0]===void 0&&(r[0]=f),u&&d===void 0&&u.set(r[0])}sx(r)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(r=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,r),Hn.delete(this)}cancel(){this.state==="scheduled"&&(Hn.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const px=n=>n.startsWith("--");function mx(n,r,s){px(r)?n.style.setProperty(r,s):n.style[r]=s}const gx=du(()=>window.ScrollTimeline!==void 0),yx={};function vx(n,r){const s=du(n);return()=>yx[r]??s()}const hm=vx(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),gi=([n,r,s,a])=>`cubic-bezier(${n}, ${r}, ${s}, ${a})`,dh={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:gi([0,.65,.55,1]),circOut:gi([.55,0,1,.45]),backIn:gi([.31,.01,.66,-.59]),backOut:gi([.33,1.53,.69,.99])};function pm(n,r){if(n)return typeof n=="function"?hm()?lm(n,r):"ease-out":qp(n)?gi(n):Array.isArray(n)?n.map(s=>pm(s,r)||dh.easeOut):dh[n]}function xx(n,r,s,{delay:a=0,duration:u=300,repeat:d=0,repeatType:f="loop",ease:h="easeOut",times:p}={},g=void 0){const y={[r]:s};p&&(y.offset=p);const x=pm(h,u);Array.isArray(x)&&(y.easing=x);const w={delay:a,duration:u,easing:Array.isArray(x)?"linear":x,fill:"both",iterations:d+1,direction:f==="reverse"?"alternate":"normal"};return g&&(w.pseudoElement=g),n.animate(y,w)}function mm(n){return typeof n=="function"&&"applyToOptions"in n}function wx({type:n,...r}){return mm(n)&&hm()?n.applyToOptions(r):(r.duration??(r.duration=300),r.ease??(r.ease="easeOut"),r)}class Sx extends Eu{constructor(r){if(super(),this.finishedTime=null,this.isStopped=!1,!r)return;const{element:s,name:a,keyframes:u,pseudoElement:d,allowFlatten:f=!1,finalKeyframe:h,onComplete:p}=r;this.isPseudoElement=!!d,this.allowFlatten=f,this.options=r,cu(typeof r.type!="string");const g=wx(r);this.animation=xx(s,a,u,g,d),g.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!d){const y=Su(u,this.options,h,this.speed);this.updateMotionValue?this.updateMotionValue(y):mx(s,a,y),this.animation.cancel()}p==null||p(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var r,s;(s=(r=this.animation).finish)==null||s.call(r)}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:r}=this;r==="idle"||r==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var r,s;this.isPseudoElement||(s=(r=this.animation).commitStyles)==null||s.call(r)}get duration(){var s,a;const r=((a=(s=this.animation.effect)==null?void 0:s.getComputedTiming)==null?void 0:a.call(s).duration)||0;return It(Number(r))}get time(){return It(Number(this.animation.currentTime)||0)}set time(r){this.finishedTime=null,this.animation.currentTime=Ft(r)}get speed(){return this.animation.playbackRate}set speed(r){r<0&&(this.finishedTime=null),this.animation.playbackRate=r}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(r){this.animation.startTime=r}attachTimeline({timeline:r,observe:s}){var a;return this.allowFlatten&&((a=this.animation.effect)==null||a.updateTiming({easing:"linear"})),this.animation.onfinish=null,r&&gx()?(this.animation.timeline=r,Ct):s(this)}}const gm={anticipate:Gp,backInOut:Qp,circInOut:Xp};function Ex(n){return n in gm}function Cx(n){typeof n.ease=="string"&&Ex(n.ease)&&(n.ease=gm[n.ease])}const fh=10;class Tx extends Sx{constructor(r){Cx(r),cm(r),super(r),r.startTime&&(this.startTime=r.startTime),this.options=r}updateMotionValue(r){const{motionValue:s,onUpdate:a,onComplete:u,element:d,...f}=this.options;if(!s)return;if(r!==void 0){s.set(r);return}const h=new Cu({...f,autoplay:!1}),p=Ft(this.finishedTime??this.time);s.setWithVelocity(h.sample(p-fh).value,h.sample(p).value,fh),h.stop()}}const hh=(n,r)=>r==="zIndex"?!1:!!(typeof n=="number"||Array.isArray(n)||typeof n=="string"&&(Cn.test(n)||n==="0")&&!n.startsWith("url("));function kx(n){const r=n[0];if(n.length===1)return!0;for(let s=0;s<n.length;s++)if(n[s]!==r)return!0}function Px(n,r,s,a){const u=n[0];if(u===null)return!1;if(r==="display"||r==="visibility")return!0;const d=n[n.length-1],f=hh(u,r),h=hh(d,r);return!f||!h?!1:kx(n)||(s==="spring"||mm(s))&&a}function ku(n){return Bp(n)&&"offsetHeight"in n}const jx=new Set(["opacity","clipPath","filter","transform"]),Nx=du(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function Rx(n){var g;const{motionValue:r,name:s,repeatDelay:a,repeatType:u,damping:d,type:f}=n;if(!ku((g=r==null?void 0:r.owner)==null?void 0:g.current))return!1;const{onUpdate:h,transformTemplate:p}=r.owner.getProps();return Nx()&&s&&jx.has(s)&&(s!=="transform"||!p)&&!h&&!a&&u!=="mirror"&&d!==0&&f!=="inertia"}const Mx=40;class Ax extends Eu{constructor({autoplay:r=!0,delay:s=0,type:a="keyframes",repeat:u=0,repeatDelay:d=0,repeatType:f="loop",keyframes:h,name:p,motionValue:g,element:y,...x}){var A;super(),this.stop=()=>{var D,M;this._animation&&(this._animation.stop(),(D=this.stopTimeline)==null||D.call(this)),(M=this.keyframeResolver)==null||M.cancel()},this.createdAt=at.now();const w={autoplay:r,delay:s,type:a,repeat:u,repeatDelay:d,repeatType:f,name:p,motionValue:g,element:y,...x},S=(y==null?void 0:y.KeyframeResolver)||Tu;this.keyframeResolver=new S(h,(D,M,L)=>this.onKeyframesResolved(D,M,w,!L),p,g,y),(A=this.keyframeResolver)==null||A.scheduleResolve()}onKeyframesResolved(r,s,a,u){this.keyframeResolver=void 0;const{name:d,type:f,velocity:h,delay:p,isHandoff:g,onUpdate:y}=a;this.resolvedAt=at.now(),Px(r,d,f,h)||((qt.instantAnimations||!p)&&(y==null||y(Su(r,a,s))),r[0]=r[r.length-1],a.duration=0,a.repeat=0);const w={startTime:u?this.resolvedAt?this.resolvedAt-this.createdAt>Mx?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:s,...a,keyframes:r},S=!g&&Rx(w)?new Tx({...w,element:w.motionValue.owner.current}):new Cu(w);S.finished.then(()=>this.notifyFinished()).catch(Ct),this.pendingTimeline&&(this.stopTimeline=S.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=S}get finished(){return this._animation?this.animation.finished:this._finished}then(r,s){return this.finished.finally(r).then(()=>{})}get animation(){var r;return this._animation||((r=this.keyframeResolver)==null||r.resume(),hx()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(r){this.animation.time=r}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(r){this.animation.speed=r}get startTime(){return this.animation.startTime}attachTimeline(r){return this._animation?this.stopTimeline=this.animation.attachTimeline(r):this.pendingTimeline=r,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var r;this._animation&&this.animation.cancel(),(r=this.keyframeResolver)==null||r.cancel()}}const Lx=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Dx(n){const r=Lx.exec(n);if(!r)return[,];const[,s,a,u]=r;return[`--${s??a}`,u]}function ym(n,r,s=1){const[a,u]=Dx(n);if(!a)return;const d=window.getComputedStyle(r).getPropertyValue(a);if(d){const f=d.trim();return Ip(f)?parseFloat(f):f}return gu(u)?ym(u,r,s+1):u}function Pu(n,r){return(n==null?void 0:n[r])??(n==null?void 0:n.default)??n}const vm=new Set(["width","height","top","left","right","bottom",...jr]),Vx={test:n=>n==="auto",parse:n=>n},xm=n=>r=>r.test(n),wm=[Pr,te,Bt,xn,k1,T1,Vx],ph=n=>wm.find(xm(n));function _x(n){return typeof n=="number"?n===0:n!==null?n==="none"||n==="0"||zp(n):!0}const bx=new Set(["brightness","contrast","saturate","opacity"]);function Ox(n){const[r,s]=n.slice(0,-1).split("(");if(r==="drop-shadow")return n;const[a]=s.match(yu)||[];if(!a)return n;const u=s.replace(a,"");let d=bx.has(r)?1:0;return a!==s&&(d*=100),r+"("+d+u+")"}const Fx=/\b([a-z-]*)\(.*?\)/gu,Ql={...Cn,getAnimatableNone:n=>{const r=n.match(Fx);return r?r.map(Ox).join(" "):n}},mh={...Pr,transform:Math.round},Ix={rotate:xn,rotateX:xn,rotateY:xn,rotateZ:xn,scale:Gs,scaleX:Gs,scaleY:Gs,scaleZ:Gs,skew:xn,skewX:xn,skewY:xn,distance:te,translateX:te,translateY:te,translateZ:te,x:te,y:te,z:te,perspective:te,transformPerspective:te,opacity:Ti,originX:th,originY:th,originZ:te},ju={borderWidth:te,borderTopWidth:te,borderRightWidth:te,borderBottomWidth:te,borderLeftWidth:te,borderRadius:te,radius:te,borderTopLeftRadius:te,borderTopRightRadius:te,borderBottomRightRadius:te,borderBottomLeftRadius:te,width:te,maxWidth:te,height:te,maxHeight:te,top:te,right:te,bottom:te,left:te,padding:te,paddingTop:te,paddingRight:te,paddingBottom:te,paddingLeft:te,margin:te,marginTop:te,marginRight:te,marginBottom:te,marginLeft:te,backgroundPositionX:te,backgroundPositionY:te,...Ix,zIndex:mh,fillOpacity:Ti,strokeOpacity:Ti,numOctaves:mh},Bx={...ju,color:De,backgroundColor:De,outlineColor:De,fill:De,stroke:De,borderColor:De,borderTopColor:De,borderRightColor:De,borderBottomColor:De,borderLeftColor:De,filter:Ql,WebkitFilter:Ql},Sm=n=>Bx[n];function Em(n,r){let s=Sm(n);return s!==Ql&&(s=Cn),s.getAnimatableNone?s.getAnimatableNone(r):void 0}const zx=new Set(["auto","none","0"]);function Ux(n,r,s){let a=0,u;for(;a<n.length&&!u;){const d=n[a];typeof d=="string"&&!zx.has(d)&&ki(d).values.length&&(u=n[a]),a++}if(u&&s)for(const d of r)n[d]=Em(s,u)}class $x extends Tu{constructor(r,s,a,u,d){super(r,s,a,u,d,!0)}readKeyframes(){const{unresolvedKeyframes:r,element:s,name:a}=this;if(!s||!s.current)return;super.readKeyframes();for(let p=0;p<r.length;p++){let g=r[p];if(typeof g=="string"&&(g=g.trim(),gu(g))){const y=ym(g,s.current);y!==void 0&&(r[p]=y),p===r.length-1&&(this.finalKeyframe=g)}}if(this.resolveNoneKeyframes(),!vm.has(a)||r.length!==2)return;const[u,d]=r,f=ph(u),h=ph(d);if(f!==h)if(ch(f)&&ch(h))for(let p=0;p<r.length;p++){const g=r[p];typeof g=="string"&&(r[p]=parseFloat(g))}else Wn[a]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:r,name:s}=this,a=[];for(let u=0;u<r.length;u++)(r[u]===null||_x(r[u]))&&a.push(u);a.length&&Ux(r,a,s)}measureInitialState(){const{element:r,unresolvedKeyframes:s,name:a}=this;if(!r||!r.current)return;a==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Wn[a](r.measureViewportBox(),window.getComputedStyle(r.current)),s[0]=this.measuredOrigin;const u=s[s.length-1];u!==void 0&&r.getValue(a,u).jump(u,!1)}measureEndState(){var h;const{element:r,name:s,unresolvedKeyframes:a}=this;if(!r||!r.current)return;const u=r.getValue(s);u&&u.jump(this.measuredOrigin,!1);const d=a.length-1,f=a[d];a[d]=Wn[s](r.measureViewportBox(),window.getComputedStyle(r.current)),f!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=f),(h=this.removedTransforms)!=null&&h.length&&this.removedTransforms.forEach(([p,g])=>{r.getValue(p).set(g)}),this.resolveNoneKeyframes()}}function Wx(n,r,s){if(n instanceof EventTarget)return[n];if(typeof n=="string"){let a=document;const u=(s==null?void 0:s[n])??a.querySelectorAll(n);return u?Array.from(u):[]}return Array.from(n)}const Cm=(n,r)=>r&&typeof n=="number"?r.transform(n):n,gh=30,Hx=n=>!isNaN(parseFloat(n));class Kx{constructor(r,s={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(a,u=!0)=>{var f,h;const d=at.now();if(this.updatedAt!==d&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(a),this.current!==this.prev&&((f=this.events.change)==null||f.notify(this.current),this.dependents))for(const p of this.dependents)p.dirty();u&&((h=this.events.renderRequest)==null||h.notify(this.current))},this.hasAnimated=!1,this.setCurrent(r),this.owner=s.owner}setCurrent(r){this.current=r,this.updatedAt=at.now(),this.canTrackVelocity===null&&r!==void 0&&(this.canTrackVelocity=Hx(this.current))}setPrevFrameValue(r=this.current){this.prevFrameValue=r,this.prevUpdatedAt=this.updatedAt}onChange(r){return this.on("change",r)}on(r,s){this.events[r]||(this.events[r]=new fu);const a=this.events[r].add(s);return r==="change"?()=>{a(),Pe.read(()=>{this.events.change.getSize()||this.stop()})}:a}clearListeners(){for(const r in this.events)this.events[r].clear()}attach(r,s){this.passiveEffect=r,this.stopPassiveEffect=s}set(r,s=!0){!s||!this.passiveEffect?this.updateAndNotify(r,s):this.passiveEffect(r,this.updateAndNotify)}setWithVelocity(r,s,a){this.set(s),this.prev=void 0,this.prevFrameValue=r,this.prevUpdatedAt=this.updatedAt-a}jump(r,s=!0){this.updateAndNotify(r),this.prev=r,this.prevUpdatedAt=this.prevFrameValue=void 0,s&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var r;(r=this.events.change)==null||r.notify(this.current)}addDependent(r){this.dependents||(this.dependents=new Set),this.dependents.add(r)}removeDependent(r){this.dependents&&this.dependents.delete(r)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const r=at.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||r-this.updatedAt>gh)return 0;const s=Math.min(this.updatedAt-this.prevUpdatedAt,gh);return Up(parseFloat(this.current)-parseFloat(this.prevFrameValue),s)}start(r){return this.stop(),new Promise(s=>{this.hasAnimated=!0,this.animation=r(s),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var r,s;(r=this.dependents)==null||r.clear(),(s=this.events.destroy)==null||s.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Tr(n,r){return new Kx(n,r)}const{schedule:Nu}=Jp(queueMicrotask,!1),Lt={x:!1,y:!1};function Tm(){return Lt.x||Lt.y}function Qx(n){return n==="x"||n==="y"?Lt[n]?null:(Lt[n]=!0,()=>{Lt[n]=!1}):Lt.x||Lt.y?null:(Lt.x=Lt.y=!0,()=>{Lt.x=Lt.y=!1})}function km(n,r){const s=Wx(n),a=new AbortController,u={passive:!0,...r,signal:a.signal};return[s,u,()=>a.abort()]}function yh(n){return!(n.pointerType==="touch"||Tm())}function Gx(n,r,s={}){const[a,u,d]=km(n,s),f=h=>{if(!yh(h))return;const{target:p}=h,g=r(p,h);if(typeof g!="function"||!p)return;const y=x=>{yh(x)&&(g(x),p.removeEventListener("pointerleave",y))};p.addEventListener("pointerleave",y,u)};return a.forEach(h=>{h.addEventListener("pointerenter",f,u)}),d}const Pm=(n,r)=>r?n===r?!0:Pm(n,r.parentElement):!1,Ru=n=>n.pointerType==="mouse"?typeof n.button!="number"||n.button<=0:n.isPrimary!==!1,Yx=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function Xx(n){return Yx.has(n.tagName)||n.tabIndex!==-1}const eo=new WeakSet;function vh(n){return r=>{r.key==="Enter"&&n(r)}}function Tl(n,r){n.dispatchEvent(new PointerEvent("pointer"+r,{isPrimary:!0,bubbles:!0}))}const Zx=(n,r)=>{const s=n.currentTarget;if(!s)return;const a=vh(()=>{if(eo.has(s))return;Tl(s,"down");const u=vh(()=>{Tl(s,"up")}),d=()=>Tl(s,"cancel");s.addEventListener("keyup",u,r),s.addEventListener("blur",d,r)});s.addEventListener("keydown",a,r),s.addEventListener("blur",()=>s.removeEventListener("keydown",a),r)};function xh(n){return Ru(n)&&!Tm()}function qx(n,r,s={}){const[a,u,d]=km(n,s),f=h=>{const p=h.currentTarget;if(!xh(h))return;eo.add(p);const g=r(p,h),y=(S,A)=>{window.removeEventListener("pointerup",x),window.removeEventListener("pointercancel",w),eo.has(p)&&eo.delete(p),xh(S)&&typeof g=="function"&&g(S,{success:A})},x=S=>{y(S,p===window||p===document||s.useGlobalTarget||Pm(p,S.target))},w=S=>{y(S,!1)};window.addEventListener("pointerup",x,u),window.addEventListener("pointercancel",w,u)};return a.forEach(h=>{(s.useGlobalTarget?window:h).addEventListener("pointerdown",f,u),ku(h)&&(h.addEventListener("focus",g=>Zx(g,u)),!Xx(h)&&!h.hasAttribute("tabindex")&&(h.tabIndex=0))}),d}function jm(n){return Bp(n)&&"ownerSVGElement"in n}function Jx(n){return jm(n)&&n.tagName==="svg"}const Ke=n=>!!(n&&n.getVelocity),ew=[...wm,De,Cn],tw=n=>ew.find(xm(n)),Mu=C.createContext({transformPagePoint:n=>n,isStatic:!1,reducedMotion:"never"});class nw extends C.Component{getSnapshotBeforeUpdate(r){const s=this.props.childRef.current;if(s&&r.isPresent&&!this.props.isPresent){const a=s.offsetParent,u=ku(a)&&a.offsetWidth||0,d=this.props.sizeRef.current;d.height=s.offsetHeight||0,d.width=s.offsetWidth||0,d.top=s.offsetTop,d.left=s.offsetLeft,d.right=u-d.width-d.left}return null}componentDidUpdate(){}render(){return this.props.children}}function rw({children:n,isPresent:r,anchorX:s}){const a=C.useId(),u=C.useRef(null),d=C.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:f}=C.useContext(Mu);return C.useInsertionEffect(()=>{const{width:h,height:p,top:g,left:y,right:x}=d.current;if(r||!u.current||!h||!p)return;const w=s==="left"?`left: ${y}`:`right: ${x}`;u.current.dataset.motionPopId=a;const S=document.createElement("style");return f&&(S.nonce=f),document.head.appendChild(S),S.sheet&&S.sheet.insertRule(`
          [data-motion-pop-id="${a}"] {
            position: absolute !important;
            width: ${h}px !important;
            height: ${p}px !important;
            ${w}px !important;
            top: ${g}px !important;
          }
        `),()=>{document.head.contains(S)&&document.head.removeChild(S)}},[r]),v.jsx(nw,{isPresent:r,childRef:u,sizeRef:d,children:C.cloneElement(n,{ref:u})})}const iw=({children:n,initial:r,isPresent:s,onExitComplete:a,custom:u,presenceAffectsLayout:d,mode:f,anchorX:h})=>{const p=ou(sw),g=C.useId();let y=!0,x=C.useMemo(()=>(y=!1,{id:g,initial:r,isPresent:s,custom:u,onExitComplete:w=>{p.set(w,!0);for(const S of p.values())if(!S)return;a&&a()},register:w=>(p.set(w,!1),()=>p.delete(w))}),[s,p,a]);return d&&y&&(x={...x}),C.useMemo(()=>{p.forEach((w,S)=>p.set(S,!1))},[s]),C.useEffect(()=>{!s&&!p.size&&a&&a()},[s]),f==="popLayout"&&(n=v.jsx(rw,{isPresent:s,anchorX:h,children:n})),v.jsx(vo.Provider,{value:x,children:n})};function sw(){return new Map}function Nm(n=!0){const r=C.useContext(vo);if(r===null)return[!0,null];const{isPresent:s,onExitComplete:a,register:u}=r,d=C.useId();C.useEffect(()=>{if(n)return u(d)},[n]);const f=C.useCallback(()=>n&&a&&a(d),[d,a,n]);return!s&&a?[!1,f]:[!0]}const Ys=n=>n.key||"";function wh(n){const r=[];return C.Children.forEach(n,s=>{C.isValidElement(s)&&r.push(s)}),r}const Gl=({children:n,custom:r,initial:s=!0,onExitComplete:a,presenceAffectsLayout:u=!0,mode:d="sync",propagate:f=!1,anchorX:h="left"})=>{const[p,g]=Nm(f),y=C.useMemo(()=>wh(n),[n]),x=f&&!p?[]:y.map(Ys),w=C.useRef(!0),S=C.useRef(y),A=ou(()=>new Map),[D,M]=C.useState(y),[L,z]=C.useState(y);Fp(()=>{w.current=!1,S.current=y;for(let U=0;U<L.length;U++){const X=Ys(L[U]);x.includes(X)?A.delete(X):A.get(X)!==!0&&A.set(X,!1)}},[L,x.length,x.join("-")]);const F=[];if(y!==D){let U=[...y];for(let X=0;X<L.length;X++){const re=L[X],Z=Ys(re);x.includes(Z)||(U.splice(X,0,re),F.push(re))}return d==="wait"&&F.length&&(U=F),z(wh(U)),M(y),null}const{forceRender:Y}=C.useContext(su);return v.jsx(v.Fragment,{children:L.map(U=>{const X=Ys(U),re=f&&!p?!1:y===L||x.includes(X),Z=()=>{if(A.has(X))A.set(X,!0);else return;let me=!0;A.forEach(we=>{we||(me=!1)}),me&&(Y==null||Y(),z(S.current),f&&(g==null||g()),a&&a())};return v.jsx(iw,{isPresent:re,initial:!w.current||s?void 0:!1,custom:r,presenceAffectsLayout:u,mode:d,onExitComplete:re?void 0:Z,anchorX:h,children:U},X)})})},Rm=C.createContext({strict:!1}),Sh={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},kr={};for(const n in Sh)kr[n]={isEnabled:r=>Sh[n].some(s=>!!r[s])};function ow(n){for(const r in n)kr[r]={...kr[r],...n[r]}}const aw=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function co(n){return n.startsWith("while")||n.startsWith("drag")&&n!=="draggable"||n.startsWith("layout")||n.startsWith("onTap")||n.startsWith("onPan")||n.startsWith("onLayout")||aw.has(n)}let Mm=n=>!co(n);function lw(n){typeof n=="function"&&(Mm=r=>r.startsWith("on")?!co(r):n(r))}try{lw(require("@emotion/is-prop-valid").default)}catch{}function uw(n,r,s){const a={};for(const u in n)u==="values"&&typeof n.values=="object"||(Mm(u)||s===!0&&co(u)||!r&&!co(u)||n.draggable&&u.startsWith("onDrag"))&&(a[u]=n[u]);return a}function cw(n){if(typeof Proxy>"u")return n;const r=new Map,s=(...a)=>n(...a);return new Proxy(s,{get:(a,u)=>u==="create"?n:(r.has(u)||r.set(u,n(u)),r.get(u))})}const xo=C.createContext({});function wo(n){return n!==null&&typeof n=="object"&&typeof n.start=="function"}function Pi(n){return typeof n=="string"||Array.isArray(n)}const Au=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Lu=["initial",...Au];function So(n){return wo(n.animate)||Lu.some(r=>Pi(n[r]))}function Am(n){return!!(So(n)||n.variants)}function dw(n,r){if(So(n)){const{initial:s,animate:a}=n;return{initial:s===!1||Pi(s)?s:void 0,animate:Pi(a)?a:void 0}}return n.inherit!==!1?r:{}}function fw(n){const{initial:r,animate:s}=dw(n,C.useContext(xo));return C.useMemo(()=>({initial:r,animate:s}),[Eh(r),Eh(s)])}function Eh(n){return Array.isArray(n)?n.join(" "):n}const hw=Symbol.for("motionComponentSymbol");function xr(n){return n&&typeof n=="object"&&Object.prototype.hasOwnProperty.call(n,"current")}function pw(n,r,s){return C.useCallback(a=>{a&&n.onMount&&n.onMount(a),r&&(a?r.mount(a):r.unmount()),s&&(typeof s=="function"?s(a):xr(s)&&(s.current=a))},[r])}const Du=n=>n.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),mw="framerAppearId",Lm="data-"+Du(mw),Dm=C.createContext({});function gw(n,r,s,a,u){var D,M;const{visualElement:d}=C.useContext(xo),f=C.useContext(Rm),h=C.useContext(vo),p=C.useContext(Mu).reducedMotion,g=C.useRef(null);a=a||f.renderer,!g.current&&a&&(g.current=a(n,{visualState:r,parent:d,props:s,presenceContext:h,blockInitialAnimation:h?h.initial===!1:!1,reducedMotionConfig:p}));const y=g.current,x=C.useContext(Dm);y&&!y.projection&&u&&(y.type==="html"||y.type==="svg")&&yw(g.current,s,u,x);const w=C.useRef(!1);C.useInsertionEffect(()=>{y&&w.current&&y.update(s,h)});const S=s[Lm],A=C.useRef(!!S&&!((D=window.MotionHandoffIsComplete)!=null&&D.call(window,S))&&((M=window.MotionHasOptimisedAnimation)==null?void 0:M.call(window,S)));return Fp(()=>{y&&(w.current=!0,window.MotionIsMounted=!0,y.updateFeatures(),Nu.render(y.render),A.current&&y.animationState&&y.animationState.animateChanges())}),C.useEffect(()=>{y&&(!A.current&&y.animationState&&y.animationState.animateChanges(),A.current&&(queueMicrotask(()=>{var L;(L=window.MotionHandoffMarkAsComplete)==null||L.call(window,S)}),A.current=!1))}),y}function yw(n,r,s,a){const{layoutId:u,layout:d,drag:f,dragConstraints:h,layoutScroll:p,layoutRoot:g,layoutCrossfade:y}=r;n.projection=new s(n.latestValues,r["data-framer-portal-id"]?void 0:Vm(n.parent)),n.projection.setOptions({layoutId:u,layout:d,alwaysMeasureLayout:!!f||h&&xr(h),visualElement:n,animationType:typeof d=="string"?d:"both",initialPromotionConfig:a,crossfade:y,layoutScroll:p,layoutRoot:g})}function Vm(n){if(n)return n.options.allowProjection!==!1?n.projection:Vm(n.parent)}function vw({preloadedFeatures:n,createVisualElement:r,useRender:s,useVisualState:a,Component:u}){n&&ow(n);function d(h,p){let g;const y={...C.useContext(Mu),...h,layoutId:xw(h)},{isStatic:x}=y,w=fw(h),S=a(h,x);if(!x&&au){ww();const A=Sw(y);g=A.MeasureLayout,w.visualElement=gw(u,S,y,r,A.ProjectionNode)}return v.jsxs(xo.Provider,{value:w,children:[g&&w.visualElement?v.jsx(g,{visualElement:w.visualElement,...y}):null,s(u,h,pw(S,w.visualElement,p),S,x,w.visualElement)]})}d.displayName=`motion.${typeof u=="string"?u:`create(${u.displayName??u.name??""})`}`;const f=C.forwardRef(d);return f[hw]=u,f}function xw({layoutId:n}){const r=C.useContext(su).id;return r&&n!==void 0?r+"-"+n:n}function ww(n,r){C.useContext(Rm).strict}function Sw(n){const{drag:r,layout:s}=kr;if(!r&&!s)return{};const a={...r,...s};return{MeasureLayout:r!=null&&r.isEnabled(n)||s!=null&&s.isEnabled(n)?a.MeasureLayout:void 0,ProjectionNode:a.ProjectionNode}}const ji={};function Ew(n){for(const r in n)ji[r]=n[r],mu(r)&&(ji[r].isCSSVariable=!0)}function _m(n,{layout:r,layoutId:s}){return Nr.has(n)||n.startsWith("origin")||(r||s!==void 0)&&(!!ji[n]||n==="opacity")}const Cw={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Tw=jr.length;function kw(n,r,s){let a="",u=!0;for(let d=0;d<Tw;d++){const f=jr[d],h=n[f];if(h===void 0)continue;let p=!0;if(typeof h=="number"?p=h===(f.startsWith("scale")?1:0):p=parseFloat(h)===0,!p||s){const g=Cm(h,ju[f]);if(!p){u=!1;const y=Cw[f]||f;a+=`${y}(${g}) `}s&&(r[f]=g)}}return a=a.trim(),s?a=s(r,u?"":a):u&&(a="none"),a}function Vu(n,r,s){const{style:a,vars:u,transformOrigin:d}=n;let f=!1,h=!1;for(const p in r){const g=r[p];if(Nr.has(p)){f=!0;continue}else if(mu(p)){u[p]=g;continue}else{const y=Cm(g,ju[p]);p.startsWith("origin")?(h=!0,d[p]=y):a[p]=y}}if(r.transform||(f||s?a.transform=kw(r,n.transform,s):a.transform&&(a.transform="none")),h){const{originX:p="50%",originY:g="50%",originZ:y=0}=d;a.transformOrigin=`${p} ${g} ${y}`}}const _u=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function bm(n,r,s){for(const a in r)!Ke(r[a])&&!_m(a,s)&&(n[a]=r[a])}function Pw({transformTemplate:n},r){return C.useMemo(()=>{const s=_u();return Vu(s,r,n),Object.assign({},s.vars,s.style)},[r])}function jw(n,r){const s=n.style||{},a={};return bm(a,s,n),Object.assign(a,Pw(n,r)),a}function Nw(n,r){const s={},a=jw(n,r);return n.drag&&n.dragListener!==!1&&(s.draggable=!1,a.userSelect=a.WebkitUserSelect=a.WebkitTouchCallout="none",a.touchAction=n.drag===!0?"none":`pan-${n.drag==="x"?"y":"x"}`),n.tabIndex===void 0&&(n.onTap||n.onTapStart||n.whileTap)&&(s.tabIndex=0),s.style=a,s}const Rw={offset:"stroke-dashoffset",array:"stroke-dasharray"},Mw={offset:"strokeDashoffset",array:"strokeDasharray"};function Aw(n,r,s=1,a=0,u=!0){n.pathLength=1;const d=u?Rw:Mw;n[d.offset]=te.transform(-a);const f=te.transform(r),h=te.transform(s);n[d.array]=`${f} ${h}`}function Om(n,{attrX:r,attrY:s,attrScale:a,pathLength:u,pathSpacing:d=1,pathOffset:f=0,...h},p,g,y){if(Vu(n,h,g),p){n.style.viewBox&&(n.attrs.viewBox=n.style.viewBox);return}n.attrs=n.style,n.style={};const{attrs:x,style:w}=n;x.transform&&(w.transform=x.transform,delete x.transform),(w.transform||x.transformOrigin)&&(w.transformOrigin=x.transformOrigin??"50% 50%",delete x.transformOrigin),w.transform&&(w.transformBox=(y==null?void 0:y.transformBox)??"fill-box",delete x.transformBox),r!==void 0&&(x.x=r),s!==void 0&&(x.y=s),a!==void 0&&(x.scale=a),u!==void 0&&Aw(x,u,d,f,!1)}const Fm=()=>({..._u(),attrs:{}}),Im=n=>typeof n=="string"&&n.toLowerCase()==="svg";function Lw(n,r,s,a){const u=C.useMemo(()=>{const d=Fm();return Om(d,r,Im(a),n.transformTemplate,n.style),{...d.attrs,style:{...d.style}}},[r]);if(n.style){const d={};bm(d,n.style,n),u.style={...d,...u.style}}return u}const Dw=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function bu(n){return typeof n!="string"||n.includes("-")?!1:!!(Dw.indexOf(n)>-1||/[A-Z]/u.test(n))}function Vw(n=!1){return(s,a,u,{latestValues:d},f)=>{const p=(bu(s)?Lw:Nw)(a,d,f,s),g=uw(a,typeof s=="string",n),y=s!==C.Fragment?{...g,...p,ref:u}:{},{children:x}=a,w=C.useMemo(()=>Ke(x)?x.get():x,[x]);return C.createElement(s,{...y,children:w})}}function Ch(n){const r=[{},{}];return n==null||n.values.forEach((s,a)=>{r[0][a]=s.get(),r[1][a]=s.getVelocity()}),r}function Ou(n,r,s,a){if(typeof r=="function"){const[u,d]=Ch(a);r=r(s!==void 0?s:n.custom,u,d)}if(typeof r=="string"&&(r=n.variants&&n.variants[r]),typeof r=="function"){const[u,d]=Ch(a);r=r(s!==void 0?s:n.custom,u,d)}return r}function to(n){return Ke(n)?n.get():n}function _w({scrapeMotionValuesFromProps:n,createRenderState:r},s,a,u){return{latestValues:bw(s,a,u,n),renderState:r()}}const Bm=n=>(r,s)=>{const a=C.useContext(xo),u=C.useContext(vo),d=()=>_w(n,r,a,u);return s?d():ou(d)};function bw(n,r,s,a){const u={},d=a(n,{});for(const w in d)u[w]=to(d[w]);let{initial:f,animate:h}=n;const p=So(n),g=Am(n);r&&g&&!p&&n.inherit!==!1&&(f===void 0&&(f=r.initial),h===void 0&&(h=r.animate));let y=s?s.initial===!1:!1;y=y||f===!1;const x=y?h:f;if(x&&typeof x!="boolean"&&!wo(x)){const w=Array.isArray(x)?x:[x];for(let S=0;S<w.length;S++){const A=Ou(n,w[S]);if(A){const{transitionEnd:D,transition:M,...L}=A;for(const z in L){let F=L[z];if(Array.isArray(F)){const Y=y?F.length-1:0;F=F[Y]}F!==null&&(u[z]=F)}for(const z in D)u[z]=D[z]}}}return u}function Fu(n,r,s){var d;const{style:a}=n,u={};for(const f in a)(Ke(a[f])||r.style&&Ke(r.style[f])||_m(f,n)||((d=s==null?void 0:s.getValue(f))==null?void 0:d.liveStyle)!==void 0)&&(u[f]=a[f]);return u}const Ow={useVisualState:Bm({scrapeMotionValuesFromProps:Fu,createRenderState:_u})};function zm(n,r,s){const a=Fu(n,r,s);for(const u in n)if(Ke(n[u])||Ke(r[u])){const d=jr.indexOf(u)!==-1?"attr"+u.charAt(0).toUpperCase()+u.substring(1):u;a[d]=n[u]}return a}const Fw={useVisualState:Bm({scrapeMotionValuesFromProps:zm,createRenderState:Fm})};function Iw(n,r){return function(a,{forwardMotionProps:u}={forwardMotionProps:!1}){const f={...bu(a)?Fw:Ow,preloadedFeatures:n,useRender:Vw(u),createVisualElement:r,Component:a};return vw(f)}}function Ni(n,r,s){const a=n.getProps();return Ou(a,r,s!==void 0?s:a.custom,n)}const Yl=n=>Array.isArray(n);function Bw(n,r,s){n.hasValue(r)?n.getValue(r).set(s):n.addValue(r,Tr(s))}function zw(n){return Yl(n)?n[n.length-1]||0:n}function Uw(n,r){const s=Ni(n,r);let{transitionEnd:a={},transition:u={},...d}=s||{};d={...d,...a};for(const f in d){const h=zw(d[f]);Bw(n,f,h)}}function $w(n){return!!(Ke(n)&&n.add)}function Xl(n,r){const s=n.getValue("willChange");if($w(s))return s.add(r);if(!s&&qt.WillChange){const a=new qt.WillChange("auto");n.addValue("willChange",a),a.add(r)}}function Um(n){return n.props[Lm]}const Ww=n=>n!==null;function Hw(n,{repeat:r,repeatType:s="loop"},a){const u=n.filter(Ww),d=r&&s!=="loop"&&r%2===1?0:u.length-1;return u[d]}const Kw={type:"spring",stiffness:500,damping:25,restSpeed:10},Qw=n=>({type:"spring",stiffness:550,damping:n===0?2*Math.sqrt(550):30,restSpeed:10}),Gw={type:"keyframes",duration:.8},Yw={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Xw=(n,{keyframes:r})=>r.length>2?Gw:Nr.has(n)?n.startsWith("scale")?Qw(r[1]):Kw:Yw;function Zw({when:n,delay:r,delayChildren:s,staggerChildren:a,staggerDirection:u,repeat:d,repeatType:f,repeatDelay:h,from:p,elapsed:g,...y}){return!!Object.keys(y).length}const Iu=(n,r,s,a={},u,d)=>f=>{const h=Pu(a,n)||{},p=h.delay||a.delay||0;let{elapsed:g=0}=a;g=g-Ft(p);const y={keyframes:Array.isArray(s)?s:[null,s],ease:"easeOut",velocity:r.getVelocity(),...h,delay:-g,onUpdate:w=>{r.set(w),h.onUpdate&&h.onUpdate(w)},onComplete:()=>{f(),h.onComplete&&h.onComplete()},name:n,motionValue:r,element:d?void 0:u};Zw(h)||Object.assign(y,Xw(n,y)),y.duration&&(y.duration=Ft(y.duration)),y.repeatDelay&&(y.repeatDelay=Ft(y.repeatDelay)),y.from!==void 0&&(y.keyframes[0]=y.from);let x=!1;if((y.type===!1||y.duration===0&&!y.repeatDelay)&&(y.duration=0,y.delay===0&&(x=!0)),(qt.instantAnimations||qt.skipAnimations)&&(x=!0,y.duration=0,y.delay=0),y.allowFlatten=!h.type&&!h.ease,x&&!d&&r.get()!==void 0){const w=Hw(y.keyframes,h);if(w!==void 0){Pe.update(()=>{y.onUpdate(w),y.onComplete()});return}}return h.isSync?new Cu(y):new Ax(y)};function qw({protectedKeys:n,needsAnimating:r},s){const a=n.hasOwnProperty(s)&&r[s]!==!0;return r[s]=!1,a}function $m(n,r,{delay:s=0,transitionOverride:a,type:u}={}){let{transition:d=n.getDefaultTransition(),transitionEnd:f,...h}=r;a&&(d=a);const p=[],g=u&&n.animationState&&n.animationState.getState()[u];for(const y in h){const x=n.getValue(y,n.latestValues[y]??null),w=h[y];if(w===void 0||g&&qw(g,y))continue;const S={delay:s,...Pu(d||{},y)},A=x.get();if(A!==void 0&&!x.isAnimating&&!Array.isArray(w)&&w===A&&!S.velocity)continue;let D=!1;if(window.MotionHandoffAnimation){const L=Um(n);if(L){const z=window.MotionHandoffAnimation(L,y,Pe);z!==null&&(S.startTime=z,D=!0)}}Xl(n,y),x.start(Iu(y,x,w,n.shouldReduceMotion&&vm.has(y)?{type:!1}:S,n,D));const M=x.animation;M&&p.push(M)}return f&&Promise.all(p).then(()=>{Pe.update(()=>{f&&Uw(n,f)})}),p}function Zl(n,r,s={}){var p;const a=Ni(n,r,s.type==="exit"?(p=n.presenceContext)==null?void 0:p.custom:void 0);let{transition:u=n.getDefaultTransition()||{}}=a||{};s.transitionOverride&&(u=s.transitionOverride);const d=a?()=>Promise.all($m(n,a,s)):()=>Promise.resolve(),f=n.variantChildren&&n.variantChildren.size?(g=0)=>{const{delayChildren:y=0,staggerChildren:x,staggerDirection:w}=u;return Jw(n,r,y+g,x,w,s)}:()=>Promise.resolve(),{when:h}=u;if(h){const[g,y]=h==="beforeChildren"?[d,f]:[f,d];return g().then(()=>y())}else return Promise.all([d(),f(s.delay)])}function Jw(n,r,s=0,a=0,u=1,d){const f=[],h=(n.variantChildren.size-1)*a,p=u===1?(g=0)=>g*a:(g=0)=>h-g*a;return Array.from(n.variantChildren).sort(e2).forEach((g,y)=>{g.notify("AnimationStart",r),f.push(Zl(g,r,{...d,delay:s+p(y)}).then(()=>g.notify("AnimationComplete",r)))}),Promise.all(f)}function e2(n,r){return n.sortNodePosition(r)}function t2(n,r,s={}){n.notify("AnimationStart",r);let a;if(Array.isArray(r)){const u=r.map(d=>Zl(n,d,s));a=Promise.all(u)}else if(typeof r=="string")a=Zl(n,r,s);else{const u=typeof r=="function"?Ni(n,r,s.custom):r;a=Promise.all($m(n,u,s))}return a.then(()=>{n.notify("AnimationComplete",r)})}function Wm(n,r){if(!Array.isArray(r))return!1;const s=r.length;if(s!==n.length)return!1;for(let a=0;a<s;a++)if(r[a]!==n[a])return!1;return!0}const n2=Lu.length;function Hm(n){if(!n)return;if(!n.isControllingVariants){const s=n.parent?Hm(n.parent)||{}:{};return n.props.initial!==void 0&&(s.initial=n.props.initial),s}const r={};for(let s=0;s<n2;s++){const a=Lu[s],u=n.props[a];(Pi(u)||u===!1)&&(r[a]=u)}return r}const r2=[...Au].reverse(),i2=Au.length;function s2(n){return r=>Promise.all(r.map(({animation:s,options:a})=>t2(n,s,a)))}function o2(n){let r=s2(n),s=Th(),a=!0;const u=p=>(g,y)=>{var w;const x=Ni(n,y,p==="exit"?(w=n.presenceContext)==null?void 0:w.custom:void 0);if(x){const{transition:S,transitionEnd:A,...D}=x;g={...g,...D,...A}}return g};function d(p){r=p(n)}function f(p){const{props:g}=n,y=Hm(n.parent)||{},x=[],w=new Set;let S={},A=1/0;for(let M=0;M<i2;M++){const L=r2[M],z=s[L],F=g[L]!==void 0?g[L]:y[L],Y=Pi(F),U=L===p?z.isActive:null;U===!1&&(A=M);let X=F===y[L]&&F!==g[L]&&Y;if(X&&a&&n.manuallyAnimateOnMount&&(X=!1),z.protectedKeys={...S},!z.isActive&&U===null||!F&&!z.prevProp||wo(F)||typeof F=="boolean")continue;const re=a2(z.prevProp,F);let Z=re||L===p&&z.isActive&&!X&&Y||M>A&&Y,me=!1;const we=Array.isArray(F)?F:[F];let qe=we.reduce(u(L),{});U===!1&&(qe={});const{prevResolvedValues:ut={}}=z,Qe={...ut,...qe},Je=se=>{Z=!0,w.has(se)&&(me=!0,w.delete(se)),z.needsAnimating[se]=!0;const B=n.getValue(se);B&&(B.liveStyle=!1)};for(const se in Qe){const B=qe[se],q=ut[se];if(S.hasOwnProperty(se))continue;let W=!1;Yl(B)&&Yl(q)?W=!Wm(B,q):W=B!==q,W?B!=null?Je(se):w.add(se):B!==void 0&&w.has(se)?Je(se):z.protectedKeys[se]=!0}z.prevProp=F,z.prevResolvedValues=qe,z.isActive&&(S={...S,...qe}),a&&n.blockInitialAnimation&&(Z=!1),Z&&(!(X&&re)||me)&&x.push(...we.map(se=>({animation:se,options:{type:L}})))}if(w.size){const M={};if(typeof g.initial!="boolean"){const L=Ni(n,Array.isArray(g.initial)?g.initial[0]:g.initial);L&&L.transition&&(M.transition=L.transition)}w.forEach(L=>{const z=n.getBaseTarget(L),F=n.getValue(L);F&&(F.liveStyle=!0),M[L]=z??null}),x.push({animation:M})}let D=!!x.length;return a&&(g.initial===!1||g.initial===g.animate)&&!n.manuallyAnimateOnMount&&(D=!1),a=!1,D?r(x):Promise.resolve()}function h(p,g){var x;if(s[p].isActive===g)return Promise.resolve();(x=n.variantChildren)==null||x.forEach(w=>{var S;return(S=w.animationState)==null?void 0:S.setActive(p,g)}),s[p].isActive=g;const y=f(p);for(const w in s)s[w].protectedKeys={};return y}return{animateChanges:f,setActive:h,setAnimateFunction:d,getState:()=>s,reset:()=>{s=Th(),a=!0}}}function a2(n,r){return typeof r=="string"?r!==n:Array.isArray(r)?!Wm(r,n):!1}function In(n=!1){return{isActive:n,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Th(){return{animate:In(!0),whileInView:In(),whileHover:In(),whileTap:In(),whileDrag:In(),whileFocus:In(),exit:In()}}class kn{constructor(r){this.isMounted=!1,this.node=r}update(){}}class l2 extends kn{constructor(r){super(r),r.animationState||(r.animationState=o2(r))}updateAnimationControlsSubscription(){const{animate:r}=this.node.getProps();wo(r)&&(this.unmountControls=r.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:r}=this.node.getProps(),{animate:s}=this.node.prevProps||{};r!==s&&this.updateAnimationControlsSubscription()}unmount(){var r;this.node.animationState.reset(),(r=this.unmountControls)==null||r.call(this)}}let u2=0;class c2 extends kn{constructor(){super(...arguments),this.id=u2++}update(){if(!this.node.presenceContext)return;const{isPresent:r,onExitComplete:s}=this.node.presenceContext,{isPresent:a}=this.node.prevPresenceContext||{};if(!this.node.animationState||r===a)return;const u=this.node.animationState.setActive("exit",!r);s&&!r&&u.then(()=>{s(this.id)})}mount(){const{register:r,onExitComplete:s}=this.node.presenceContext||{};s&&s(this.id),r&&(this.unmount=r(this.id))}unmount(){}}const d2={animation:{Feature:l2},exit:{Feature:c2}};function Ri(n,r,s,a={passive:!0}){return n.addEventListener(r,s,a),()=>n.removeEventListener(r,s)}function Vi(n){return{point:{x:n.pageX,y:n.pageY}}}const f2=n=>r=>Ru(r)&&n(r,Vi(r));function wi(n,r,s,a){return Ri(n,r,f2(s),a)}function Km({top:n,left:r,right:s,bottom:a}){return{x:{min:r,max:s},y:{min:n,max:a}}}function h2({x:n,y:r}){return{top:r.min,right:n.max,bottom:r.max,left:n.min}}function p2(n,r){if(!r)return n;const s=r({x:n.left,y:n.top}),a=r({x:n.right,y:n.bottom});return{top:s.y,left:s.x,bottom:a.y,right:a.x}}const Qm=1e-4,m2=1-Qm,g2=1+Qm,Gm=.01,y2=0-Gm,v2=0+Gm;function Ze(n){return n.max-n.min}function x2(n,r,s){return Math.abs(n-r)<=s}function kh(n,r,s,a=.5){n.origin=a,n.originPoint=ke(r.min,r.max,n.origin),n.scale=Ze(s)/Ze(r),n.translate=ke(s.min,s.max,n.origin)-n.originPoint,(n.scale>=m2&&n.scale<=g2||isNaN(n.scale))&&(n.scale=1),(n.translate>=y2&&n.translate<=v2||isNaN(n.translate))&&(n.translate=0)}function Si(n,r,s,a){kh(n.x,r.x,s.x,a?a.originX:void 0),kh(n.y,r.y,s.y,a?a.originY:void 0)}function Ph(n,r,s){n.min=s.min+r.min,n.max=n.min+Ze(r)}function w2(n,r,s){Ph(n.x,r.x,s.x),Ph(n.y,r.y,s.y)}function jh(n,r,s){n.min=r.min-s.min,n.max=n.min+Ze(r)}function Ei(n,r,s){jh(n.x,r.x,s.x),jh(n.y,r.y,s.y)}const Nh=()=>({translate:0,scale:1,origin:0,originPoint:0}),wr=()=>({x:Nh(),y:Nh()}),Rh=()=>({min:0,max:0}),Ae=()=>({x:Rh(),y:Rh()});function Et(n){return[n("x"),n("y")]}function kl(n){return n===void 0||n===1}function ql({scale:n,scaleX:r,scaleY:s}){return!kl(n)||!kl(r)||!kl(s)}function Bn(n){return ql(n)||Ym(n)||n.z||n.rotate||n.rotateX||n.rotateY||n.skewX||n.skewY}function Ym(n){return Mh(n.x)||Mh(n.y)}function Mh(n){return n&&n!=="0%"}function fo(n,r,s){const a=n-s,u=r*a;return s+u}function Ah(n,r,s,a,u){return u!==void 0&&(n=fo(n,u,a)),fo(n,s,a)+r}function Jl(n,r=0,s=1,a,u){n.min=Ah(n.min,r,s,a,u),n.max=Ah(n.max,r,s,a,u)}function Xm(n,{x:r,y:s}){Jl(n.x,r.translate,r.scale,r.originPoint),Jl(n.y,s.translate,s.scale,s.originPoint)}const Lh=.999999999999,Dh=1.0000000000001;function S2(n,r,s,a=!1){const u=s.length;if(!u)return;r.x=r.y=1;let d,f;for(let h=0;h<u;h++){d=s[h],f=d.projectionDelta;const{visualElement:p}=d.options;p&&p.props.style&&p.props.style.display==="contents"||(a&&d.options.layoutScroll&&d.scroll&&d!==d.root&&Er(n,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),f&&(r.x*=f.x.scale,r.y*=f.y.scale,Xm(n,f)),a&&Bn(d.latestValues)&&Er(n,d.latestValues))}r.x<Dh&&r.x>Lh&&(r.x=1),r.y<Dh&&r.y>Lh&&(r.y=1)}function Sr(n,r){n.min=n.min+r,n.max=n.max+r}function Vh(n,r,s,a,u=.5){const d=ke(n.min,n.max,u);Jl(n,r,s,d,a)}function Er(n,r){Vh(n.x,r.x,r.scaleX,r.scale,r.originX),Vh(n.y,r.y,r.scaleY,r.scale,r.originY)}function Zm(n,r){return Km(p2(n.getBoundingClientRect(),r))}function E2(n,r,s){const a=Zm(n,s),{scroll:u}=r;return u&&(Sr(a.x,u.offset.x),Sr(a.y,u.offset.y)),a}const qm=({current:n})=>n?n.ownerDocument.defaultView:null,_h=(n,r)=>Math.abs(n-r);function C2(n,r){const s=_h(n.x,r.x),a=_h(n.y,r.y);return Math.sqrt(s**2+a**2)}class Jm{constructor(r,s,{transformPagePoint:a,contextWindow:u,dragSnapToOrigin:d=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const x=jl(this.lastMoveEventInfo,this.history),w=this.startEvent!==null,S=C2(x.offset,{x:0,y:0})>=3;if(!w&&!S)return;const{point:A}=x,{timestamp:D}=ze;this.history.push({...A,timestamp:D});const{onStart:M,onMove:L}=this.handlers;w||(M&&M(this.lastMoveEvent,x),this.startEvent=this.lastMoveEvent),L&&L(this.lastMoveEvent,x)},this.handlePointerMove=(x,w)=>{this.lastMoveEvent=x,this.lastMoveEventInfo=Pl(w,this.transformPagePoint),Pe.update(this.updatePoint,!0)},this.handlePointerUp=(x,w)=>{this.end();const{onEnd:S,onSessionEnd:A,resumeAnimation:D}=this.handlers;if(this.dragSnapToOrigin&&D&&D(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const M=jl(x.type==="pointercancel"?this.lastMoveEventInfo:Pl(w,this.transformPagePoint),this.history);this.startEvent&&S&&S(x,M),A&&A(x,M)},!Ru(r))return;this.dragSnapToOrigin=d,this.handlers=s,this.transformPagePoint=a,this.contextWindow=u||window;const f=Vi(r),h=Pl(f,this.transformPagePoint),{point:p}=h,{timestamp:g}=ze;this.history=[{...p,timestamp:g}];const{onSessionStart:y}=s;y&&y(r,jl(h,this.history)),this.removeListeners=Ai(wi(this.contextWindow,"pointermove",this.handlePointerMove),wi(this.contextWindow,"pointerup",this.handlePointerUp),wi(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(r){this.handlers=r}end(){this.removeListeners&&this.removeListeners(),En(this.updatePoint)}}function Pl(n,r){return r?{point:r(n.point)}:n}function bh(n,r){return{x:n.x-r.x,y:n.y-r.y}}function jl({point:n},r){return{point:n,delta:bh(n,e0(r)),offset:bh(n,T2(r)),velocity:k2(r,.1)}}function T2(n){return n[0]}function e0(n){return n[n.length-1]}function k2(n,r){if(n.length<2)return{x:0,y:0};let s=n.length-1,a=null;const u=e0(n);for(;s>=0&&(a=n[s],!(u.timestamp-a.timestamp>Ft(r)));)s--;if(!a)return{x:0,y:0};const d=It(u.timestamp-a.timestamp);if(d===0)return{x:0,y:0};const f={x:(u.x-a.x)/d,y:(u.y-a.y)/d};return f.x===1/0&&(f.x=0),f.y===1/0&&(f.y=0),f}function P2(n,{min:r,max:s},a){return r!==void 0&&n<r?n=a?ke(r,n,a.min):Math.max(n,r):s!==void 0&&n>s&&(n=a?ke(s,n,a.max):Math.min(n,s)),n}function Oh(n,r,s){return{min:r!==void 0?n.min+r:void 0,max:s!==void 0?n.max+s-(n.max-n.min):void 0}}function j2(n,{top:r,left:s,bottom:a,right:u}){return{x:Oh(n.x,s,u),y:Oh(n.y,r,a)}}function Fh(n,r){let s=r.min-n.min,a=r.max-n.max;return r.max-r.min<n.max-n.min&&([s,a]=[a,s]),{min:s,max:a}}function N2(n,r){return{x:Fh(n.x,r.x),y:Fh(n.y,r.y)}}function R2(n,r){let s=.5;const a=Ze(n),u=Ze(r);return u>a?s=Ci(r.min,r.max-a,n.min):a>u&&(s=Ci(n.min,n.max-u,r.min)),Zt(0,1,s)}function M2(n,r){const s={};return r.min!==void 0&&(s.min=r.min-n.min),r.max!==void 0&&(s.max=r.max-n.min),s}const eu=.35;function A2(n=eu){return n===!1?n=0:n===!0&&(n=eu),{x:Ih(n,"left","right"),y:Ih(n,"top","bottom")}}function Ih(n,r,s){return{min:Bh(n,r),max:Bh(n,s)}}function Bh(n,r){return typeof n=="number"?n:n[r]||0}const L2=new WeakMap;class D2{constructor(r){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=Ae(),this.visualElement=r}start(r,{snapToCursor:s=!1}={}){const{presenceContext:a}=this.visualElement;if(a&&a.isPresent===!1)return;const u=y=>{const{dragSnapToOrigin:x}=this.getProps();x?this.pauseAnimation():this.stopAnimation(),s&&this.snapToCursor(Vi(y).point)},d=(y,x)=>{const{drag:w,dragPropagation:S,onDragStart:A}=this.getProps();if(w&&!S&&(this.openDragLock&&this.openDragLock(),this.openDragLock=Qx(w),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Et(M=>{let L=this.getAxisMotionValue(M).get()||0;if(Bt.test(L)){const{projection:z}=this.visualElement;if(z&&z.layout){const F=z.layout.layoutBox[M];F&&(L=Ze(F)*(parseFloat(L)/100))}}this.originPoint[M]=L}),A&&Pe.postRender(()=>A(y,x)),Xl(this.visualElement,"transform");const{animationState:D}=this.visualElement;D&&D.setActive("whileDrag",!0)},f=(y,x)=>{const{dragPropagation:w,dragDirectionLock:S,onDirectionLock:A,onDrag:D}=this.getProps();if(!w&&!this.openDragLock)return;const{offset:M}=x;if(S&&this.currentDirection===null){this.currentDirection=V2(M),this.currentDirection!==null&&A&&A(this.currentDirection);return}this.updateAxis("x",x.point,M),this.updateAxis("y",x.point,M),this.visualElement.render(),D&&D(y,x)},h=(y,x)=>this.stop(y,x),p=()=>Et(y=>{var x;return this.getAnimationState(y)==="paused"&&((x=this.getAxisMotionValue(y).animation)==null?void 0:x.play())}),{dragSnapToOrigin:g}=this.getProps();this.panSession=new Jm(r,{onSessionStart:u,onStart:d,onMove:f,onSessionEnd:h,resumeAnimation:p},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:g,contextWindow:qm(this.visualElement)})}stop(r,s){const a=this.isDragging;if(this.cancel(),!a)return;const{velocity:u}=s;this.startAnimation(u);const{onDragEnd:d}=this.getProps();d&&Pe.postRender(()=>d(r,s))}cancel(){this.isDragging=!1;const{projection:r,animationState:s}=this.visualElement;r&&(r.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:a}=this.getProps();!a&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),s&&s.setActive("whileDrag",!1)}updateAxis(r,s,a){const{drag:u}=this.getProps();if(!a||!Xs(r,u,this.currentDirection))return;const d=this.getAxisMotionValue(r);let f=this.originPoint[r]+a[r];this.constraints&&this.constraints[r]&&(f=P2(f,this.constraints[r],this.elastic[r])),d.set(f)}resolveConstraints(){var d;const{dragConstraints:r,dragElastic:s}=this.getProps(),a=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(d=this.visualElement.projection)==null?void 0:d.layout,u=this.constraints;r&&xr(r)?this.constraints||(this.constraints=this.resolveRefConstraints()):r&&a?this.constraints=j2(a.layoutBox,r):this.constraints=!1,this.elastic=A2(s),u!==this.constraints&&a&&this.constraints&&!this.hasMutatedConstraints&&Et(f=>{this.constraints!==!1&&this.getAxisMotionValue(f)&&(this.constraints[f]=M2(a.layoutBox[f],this.constraints[f]))})}resolveRefConstraints(){const{dragConstraints:r,onMeasureDragConstraints:s}=this.getProps();if(!r||!xr(r))return!1;const a=r.current,{projection:u}=this.visualElement;if(!u||!u.layout)return!1;const d=E2(a,u.root,this.visualElement.getTransformPagePoint());let f=N2(u.layout.layoutBox,d);if(s){const h=s(h2(f));this.hasMutatedConstraints=!!h,h&&(f=Km(h))}return f}startAnimation(r){const{drag:s,dragMomentum:a,dragElastic:u,dragTransition:d,dragSnapToOrigin:f,onDragTransitionEnd:h}=this.getProps(),p=this.constraints||{},g=Et(y=>{if(!Xs(y,s,this.currentDirection))return;let x=p&&p[y]||{};f&&(x={min:0,max:0});const w=u?200:1e6,S=u?40:1e7,A={type:"inertia",velocity:a?r[y]:0,bounceStiffness:w,bounceDamping:S,timeConstant:750,restDelta:1,restSpeed:10,...d,...x};return this.startAxisValueAnimation(y,A)});return Promise.all(g).then(h)}startAxisValueAnimation(r,s){const a=this.getAxisMotionValue(r);return Xl(this.visualElement,r),a.start(Iu(r,a,0,s,this.visualElement,!1))}stopAnimation(){Et(r=>this.getAxisMotionValue(r).stop())}pauseAnimation(){Et(r=>{var s;return(s=this.getAxisMotionValue(r).animation)==null?void 0:s.pause()})}getAnimationState(r){var s;return(s=this.getAxisMotionValue(r).animation)==null?void 0:s.state}getAxisMotionValue(r){const s=`_drag${r.toUpperCase()}`,a=this.visualElement.getProps(),u=a[s];return u||this.visualElement.getValue(r,(a.initial?a.initial[r]:void 0)||0)}snapToCursor(r){Et(s=>{const{drag:a}=this.getProps();if(!Xs(s,a,this.currentDirection))return;const{projection:u}=this.visualElement,d=this.getAxisMotionValue(s);if(u&&u.layout){const{min:f,max:h}=u.layout.layoutBox[s];d.set(r[s]-ke(f,h,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:r,dragConstraints:s}=this.getProps(),{projection:a}=this.visualElement;if(!xr(s)||!a||!this.constraints)return;this.stopAnimation();const u={x:0,y:0};Et(f=>{const h=this.getAxisMotionValue(f);if(h&&this.constraints!==!1){const p=h.get();u[f]=R2({min:p,max:p},this.constraints[f])}});const{transformTemplate:d}=this.visualElement.getProps();this.visualElement.current.style.transform=d?d({},""):"none",a.root&&a.root.updateScroll(),a.updateLayout(),this.resolveConstraints(),Et(f=>{if(!Xs(f,r,null))return;const h=this.getAxisMotionValue(f),{min:p,max:g}=this.constraints[f];h.set(ke(p,g,u[f]))})}addListeners(){if(!this.visualElement.current)return;L2.set(this.visualElement,this);const r=this.visualElement.current,s=wi(r,"pointerdown",p=>{const{drag:g,dragListener:y=!0}=this.getProps();g&&y&&this.start(p)}),a=()=>{const{dragConstraints:p}=this.getProps();xr(p)&&p.current&&(this.constraints=this.resolveRefConstraints())},{projection:u}=this.visualElement,d=u.addEventListener("measure",a);u&&!u.layout&&(u.root&&u.root.updateScroll(),u.updateLayout()),Pe.read(a);const f=Ri(window,"resize",()=>this.scalePositionWithinConstraints()),h=u.addEventListener("didUpdate",({delta:p,hasLayoutChanged:g})=>{this.isDragging&&g&&(Et(y=>{const x=this.getAxisMotionValue(y);x&&(this.originPoint[y]+=p[y].translate,x.set(x.get()+p[y].translate))}),this.visualElement.render())});return()=>{f(),s(),d(),h&&h()}}getProps(){const r=this.visualElement.getProps(),{drag:s=!1,dragDirectionLock:a=!1,dragPropagation:u=!1,dragConstraints:d=!1,dragElastic:f=eu,dragMomentum:h=!0}=r;return{...r,drag:s,dragDirectionLock:a,dragPropagation:u,dragConstraints:d,dragElastic:f,dragMomentum:h}}}function Xs(n,r,s){return(r===!0||r===n)&&(s===null||s===n)}function V2(n,r=10){let s=null;return Math.abs(n.y)>r?s="y":Math.abs(n.x)>r&&(s="x"),s}class _2 extends kn{constructor(r){super(r),this.removeGroupControls=Ct,this.removeListeners=Ct,this.controls=new D2(r)}mount(){const{dragControls:r}=this.node.getProps();r&&(this.removeGroupControls=r.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Ct}unmount(){this.removeGroupControls(),this.removeListeners()}}const zh=n=>(r,s)=>{n&&Pe.postRender(()=>n(r,s))};class b2 extends kn{constructor(){super(...arguments),this.removePointerDownListener=Ct}onPointerDown(r){this.session=new Jm(r,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:qm(this.node)})}createPanHandlers(){const{onPanSessionStart:r,onPanStart:s,onPan:a,onPanEnd:u}=this.node.getProps();return{onSessionStart:zh(r),onStart:zh(s),onMove:a,onEnd:(d,f)=>{delete this.session,u&&Pe.postRender(()=>u(d,f))}}}mount(){this.removePointerDownListener=wi(this.node.current,"pointerdown",r=>this.onPointerDown(r))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const no={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Uh(n,r){return r.max===r.min?0:n/(r.max-r.min)*100}const mi={correct:(n,r)=>{if(!r.target)return n;if(typeof n=="string")if(te.test(n))n=parseFloat(n);else return n;const s=Uh(n,r.target.x),a=Uh(n,r.target.y);return`${s}% ${a}%`}},O2={correct:(n,{treeScale:r,projectionDelta:s})=>{const a=n,u=Cn.parse(n);if(u.length>5)return a;const d=Cn.createTransformer(n),f=typeof u[0]!="number"?1:0,h=s.x.scale*r.x,p=s.y.scale*r.y;u[0+f]/=h,u[1+f]/=p;const g=ke(h,p,.5);return typeof u[2+f]=="number"&&(u[2+f]/=g),typeof u[3+f]=="number"&&(u[3+f]/=g),d(u)}};class F2 extends C.Component{componentDidMount(){const{visualElement:r,layoutGroup:s,switchLayoutGroup:a,layoutId:u}=this.props,{projection:d}=r;Ew(I2),d&&(s.group&&s.group.add(d),a&&a.register&&u&&a.register(d),d.root.didUpdate(),d.addEventListener("animationComplete",()=>{this.safeToRemove()}),d.setOptions({...d.options,onExitComplete:()=>this.safeToRemove()})),no.hasEverUpdated=!0}getSnapshotBeforeUpdate(r){const{layoutDependency:s,visualElement:a,drag:u,isPresent:d}=this.props,{projection:f}=a;return f&&(f.isPresent=d,u||r.layoutDependency!==s||s===void 0||r.isPresent!==d?f.willUpdate():this.safeToRemove(),r.isPresent!==d&&(d?f.promote():f.relegate()||Pe.postRender(()=>{const h=f.getStack();(!h||!h.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:r}=this.props.visualElement;r&&(r.root.didUpdate(),Nu.postRender(()=>{!r.currentAnimation&&r.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:r,layoutGroup:s,switchLayoutGroup:a}=this.props,{projection:u}=r;u&&(u.scheduleCheckAfterUnmount(),s&&s.group&&s.group.remove(u),a&&a.deregister&&a.deregister(u))}safeToRemove(){const{safeToRemove:r}=this.props;r&&r()}render(){return null}}function t0(n){const[r,s]=Nm(),a=C.useContext(su);return v.jsx(F2,{...n,layoutGroup:a,switchLayoutGroup:C.useContext(Dm),isPresent:r,safeToRemove:s})}const I2={borderRadius:{...mi,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:mi,borderTopRightRadius:mi,borderBottomLeftRadius:mi,borderBottomRightRadius:mi,boxShadow:O2};function B2(n,r,s){const a=Ke(n)?n:Tr(n);return a.start(Iu("",a,r,s)),a.animation}const z2=(n,r)=>n.depth-r.depth;class U2{constructor(){this.children=[],this.isDirty=!1}add(r){lu(this.children,r),this.isDirty=!0}remove(r){uu(this.children,r),this.isDirty=!0}forEach(r){this.isDirty&&this.children.sort(z2),this.isDirty=!1,this.children.forEach(r)}}function $2(n,r){const s=at.now(),a=({timestamp:u})=>{const d=u-s;d>=r&&(En(a),n(d-r))};return Pe.setup(a,!0),()=>En(a)}const n0=["TopLeft","TopRight","BottomLeft","BottomRight"],W2=n0.length,$h=n=>typeof n=="string"?parseFloat(n):n,Wh=n=>typeof n=="number"||te.test(n);function H2(n,r,s,a,u,d){u?(n.opacity=ke(0,s.opacity??1,K2(a)),n.opacityExit=ke(r.opacity??1,0,Q2(a))):d&&(n.opacity=ke(r.opacity??1,s.opacity??1,a));for(let f=0;f<W2;f++){const h=`border${n0[f]}Radius`;let p=Hh(r,h),g=Hh(s,h);if(p===void 0&&g===void 0)continue;p||(p=0),g||(g=0),p===0||g===0||Wh(p)===Wh(g)?(n[h]=Math.max(ke($h(p),$h(g),a),0),(Bt.test(g)||Bt.test(p))&&(n[h]+="%")):n[h]=g}(r.rotate||s.rotate)&&(n.rotate=ke(r.rotate||0,s.rotate||0,a))}function Hh(n,r){return n[r]!==void 0?n[r]:n.borderRadius}const K2=r0(0,.5,Yp),Q2=r0(.5,.95,Ct);function r0(n,r,s){return a=>a<n?0:a>r?1:s(Ci(n,r,a))}function Kh(n,r){n.min=r.min,n.max=r.max}function St(n,r){Kh(n.x,r.x),Kh(n.y,r.y)}function Qh(n,r){n.translate=r.translate,n.scale=r.scale,n.originPoint=r.originPoint,n.origin=r.origin}function Gh(n,r,s,a,u){return n-=r,n=fo(n,1/s,a),u!==void 0&&(n=fo(n,1/u,a)),n}function G2(n,r=0,s=1,a=.5,u,d=n,f=n){if(Bt.test(r)&&(r=parseFloat(r),r=ke(f.min,f.max,r/100)-f.min),typeof r!="number")return;let h=ke(d.min,d.max,a);n===d&&(h-=r),n.min=Gh(n.min,r,s,h,u),n.max=Gh(n.max,r,s,h,u)}function Yh(n,r,[s,a,u],d,f){G2(n,r[s],r[a],r[u],r.scale,d,f)}const Y2=["x","scaleX","originX"],X2=["y","scaleY","originY"];function Xh(n,r,s,a){Yh(n.x,r,Y2,s?s.x:void 0,a?a.x:void 0),Yh(n.y,r,X2,s?s.y:void 0,a?a.y:void 0)}function Zh(n){return n.translate===0&&n.scale===1}function i0(n){return Zh(n.x)&&Zh(n.y)}function qh(n,r){return n.min===r.min&&n.max===r.max}function Z2(n,r){return qh(n.x,r.x)&&qh(n.y,r.y)}function Jh(n,r){return Math.round(n.min)===Math.round(r.min)&&Math.round(n.max)===Math.round(r.max)}function s0(n,r){return Jh(n.x,r.x)&&Jh(n.y,r.y)}function ep(n){return Ze(n.x)/Ze(n.y)}function tp(n,r){return n.translate===r.translate&&n.scale===r.scale&&n.originPoint===r.originPoint}class q2{constructor(){this.members=[]}add(r){lu(this.members,r),r.scheduleRender()}remove(r){if(uu(this.members,r),r===this.prevLead&&(this.prevLead=void 0),r===this.lead){const s=this.members[this.members.length-1];s&&this.promote(s)}}relegate(r){const s=this.members.findIndex(u=>r===u);if(s===0)return!1;let a;for(let u=s;u>=0;u--){const d=this.members[u];if(d.isPresent!==!1){a=d;break}}return a?(this.promote(a),!0):!1}promote(r,s){const a=this.lead;if(r!==a&&(this.prevLead=a,this.lead=r,r.show(),a)){a.instance&&a.scheduleRender(),r.scheduleRender(),r.resumeFrom=a,s&&(r.resumeFrom.preserveOpacity=!0),a.snapshot&&(r.snapshot=a.snapshot,r.snapshot.latestValues=a.animationValues||a.latestValues),r.root&&r.root.isUpdating&&(r.isLayoutDirty=!0);const{crossfade:u}=r.options;u===!1&&a.hide()}}exitAnimationComplete(){this.members.forEach(r=>{const{options:s,resumingFrom:a}=r;s.onExitComplete&&s.onExitComplete(),a&&a.options.onExitComplete&&a.options.onExitComplete()})}scheduleRender(){this.members.forEach(r=>{r.instance&&r.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function J2(n,r,s){let a="";const u=n.x.translate/r.x,d=n.y.translate/r.y,f=(s==null?void 0:s.z)||0;if((u||d||f)&&(a=`translate3d(${u}px, ${d}px, ${f}px) `),(r.x!==1||r.y!==1)&&(a+=`scale(${1/r.x}, ${1/r.y}) `),s){const{transformPerspective:g,rotate:y,rotateX:x,rotateY:w,skewX:S,skewY:A}=s;g&&(a=`perspective(${g}px) ${a}`),y&&(a+=`rotate(${y}deg) `),x&&(a+=`rotateX(${x}deg) `),w&&(a+=`rotateY(${w}deg) `),S&&(a+=`skewX(${S}deg) `),A&&(a+=`skewY(${A}deg) `)}const h=n.x.scale*r.x,p=n.y.scale*r.y;return(h!==1||p!==1)&&(a+=`scale(${h}, ${p})`),a||"none"}const Nl=["","X","Y","Z"],eS={visibility:"hidden"},tS=1e3;let nS=0;function Rl(n,r,s,a){const{latestValues:u}=r;u[n]&&(s[n]=u[n],r.setStaticValue(n,0),a&&(a[n]=0))}function o0(n){if(n.hasCheckedOptimisedAppear=!0,n.root===n)return;const{visualElement:r}=n.options;if(!r)return;const s=Um(r);if(window.MotionHasOptimisedAnimation(s,"transform")){const{layout:u,layoutId:d}=n.options;window.MotionCancelOptimisedAnimation(s,"transform",Pe,!(u||d))}const{parent:a}=n;a&&!a.hasCheckedOptimisedAppear&&o0(a)}function a0({attachResizeListener:n,defaultParent:r,measureScroll:s,checkIsScrollRoot:a,resetTransform:u}){return class{constructor(f={},h=r==null?void 0:r()){this.id=nS++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(sS),this.nodes.forEach(cS),this.nodes.forEach(dS),this.nodes.forEach(oS)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=f,this.root=h?h.root||h:this,this.path=h?[...h.path,h]:[],this.parent=h,this.depth=h?h.depth+1:0;for(let p=0;p<this.path.length;p++)this.path[p].shouldResetTransform=!0;this.root===this&&(this.nodes=new U2)}addEventListener(f,h){return this.eventHandlers.has(f)||this.eventHandlers.set(f,new fu),this.eventHandlers.get(f).add(h)}notifyListeners(f,...h){const p=this.eventHandlers.get(f);p&&p.notify(...h)}hasListeners(f){return this.eventHandlers.has(f)}mount(f){if(this.instance)return;this.isSVG=jm(f)&&!Jx(f),this.instance=f;const{layoutId:h,layout:p,visualElement:g}=this.options;if(g&&!g.current&&g.mount(f),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(p||h)&&(this.isLayoutDirty=!0),n){let y;const x=()=>this.root.updateBlockedByResize=!1;n(f,()=>{this.root.updateBlockedByResize=!0,y&&y(),y=$2(x,250),no.hasAnimatedSinceResize&&(no.hasAnimatedSinceResize=!1,this.nodes.forEach(rp))})}h&&this.root.registerSharedNode(h,this),this.options.animate!==!1&&g&&(h||p)&&this.addEventListener("didUpdate",({delta:y,hasLayoutChanged:x,hasRelativeLayoutChanged:w,layout:S})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const A=this.options.transition||g.getDefaultTransition()||gS,{onLayoutAnimationStart:D,onLayoutAnimationComplete:M}=g.getProps(),L=!this.targetLayout||!s0(this.targetLayout,S),z=!x&&w;if(this.options.layoutRoot||this.resumeFrom||z||x&&(L||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const F={...Pu(A,"layout"),onPlay:D,onComplete:M};(g.shouldReduceMotion||this.options.layoutRoot)&&(F.delay=0,F.type=!1),this.startAnimation(F),this.setAnimationOrigin(y,z)}else x||rp(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=S})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const f=this.getStack();f&&f.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),En(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(fS),this.animationId++)}getTransformTemplate(){const{visualElement:f}=this.options;return f&&f.getProps().transformTemplate}willUpdate(f=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&o0(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let y=0;y<this.path.length;y++){const x=this.path[y];x.shouldResetTransform=!0,x.updateScroll("snapshot"),x.options.layoutRoot&&x.willUpdate(!1)}const{layoutId:h,layout:p}=this.options;if(h===void 0&&!p)return;const g=this.getTransformTemplate();this.prevTransformTemplateValue=g?g(this.latestValues,""):void 0,this.updateSnapshot(),f&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(np);return}this.isUpdating||this.nodes.forEach(lS),this.isUpdating=!1,this.nodes.forEach(uS),this.nodes.forEach(rS),this.nodes.forEach(iS),this.clearAllSnapshots();const h=at.now();ze.delta=Zt(0,1e3/60,h-ze.timestamp),ze.timestamp=h,ze.isProcessing=!0,wl.update.process(ze),wl.preRender.process(ze),wl.render.process(ze),ze.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Nu.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(aS),this.sharedNodes.forEach(hS)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Pe.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Pe.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!Ze(this.snapshot.measuredBox.x)&&!Ze(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let p=0;p<this.path.length;p++)this.path[p].updateScroll();const f=this.layout;this.layout=this.measure(!1),this.layoutCorrected=Ae(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:h}=this.options;h&&h.notify("LayoutMeasure",this.layout.layoutBox,f?f.layoutBox:void 0)}updateScroll(f="measure"){let h=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===f&&(h=!1),h&&this.instance){const p=a(this.instance);this.scroll={animationId:this.root.animationId,phase:f,isRoot:p,offset:s(this.instance),wasRoot:this.scroll?this.scroll.isRoot:p}}}resetTransform(){if(!u)return;const f=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,h=this.projectionDelta&&!i0(this.projectionDelta),p=this.getTransformTemplate(),g=p?p(this.latestValues,""):void 0,y=g!==this.prevTransformTemplateValue;f&&this.instance&&(h||Bn(this.latestValues)||y)&&(u(this.instance,g),this.shouldResetTransform=!1,this.scheduleRender())}measure(f=!0){const h=this.measurePageBox();let p=this.removeElementScroll(h);return f&&(p=this.removeTransform(p)),yS(p),{animationId:this.root.animationId,measuredBox:h,layoutBox:p,latestValues:{},source:this.id}}measurePageBox(){var g;const{visualElement:f}=this.options;if(!f)return Ae();const h=f.measureViewportBox();if(!(((g=this.scroll)==null?void 0:g.wasRoot)||this.path.some(vS))){const{scroll:y}=this.root;y&&(Sr(h.x,y.offset.x),Sr(h.y,y.offset.y))}return h}removeElementScroll(f){var p;const h=Ae();if(St(h,f),(p=this.scroll)!=null&&p.wasRoot)return h;for(let g=0;g<this.path.length;g++){const y=this.path[g],{scroll:x,options:w}=y;y!==this.root&&x&&w.layoutScroll&&(x.wasRoot&&St(h,f),Sr(h.x,x.offset.x),Sr(h.y,x.offset.y))}return h}applyTransform(f,h=!1){const p=Ae();St(p,f);for(let g=0;g<this.path.length;g++){const y=this.path[g];!h&&y.options.layoutScroll&&y.scroll&&y!==y.root&&Er(p,{x:-y.scroll.offset.x,y:-y.scroll.offset.y}),Bn(y.latestValues)&&Er(p,y.latestValues)}return Bn(this.latestValues)&&Er(p,this.latestValues),p}removeTransform(f){const h=Ae();St(h,f);for(let p=0;p<this.path.length;p++){const g=this.path[p];if(!g.instance||!Bn(g.latestValues))continue;ql(g.latestValues)&&g.updateSnapshot();const y=Ae(),x=g.measurePageBox();St(y,x),Xh(h,g.latestValues,g.snapshot?g.snapshot.layoutBox:void 0,y)}return Bn(this.latestValues)&&Xh(h,this.latestValues),h}setTargetDelta(f){this.targetDelta=f,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(f){this.options={...this.options,...f,crossfade:f.crossfade!==void 0?f.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==ze.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(f=!1){var w;const h=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=h.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=h.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=h.isSharedProjectionDirty);const p=!!this.resumingFrom||this!==h;if(!(f||p&&this.isSharedProjectionDirty||this.isProjectionDirty||(w=this.parent)!=null&&w.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:y,layoutId:x}=this.options;if(!(!this.layout||!(y||x))){if(this.resolvedRelativeTargetAt=ze.timestamp,!this.targetDelta&&!this.relativeTarget){const S=this.getClosestProjectingParent();S&&S.layout&&this.animationProgress!==1?(this.relativeParent=S,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Ae(),this.relativeTargetOrigin=Ae(),Ei(this.relativeTargetOrigin,this.layout.layoutBox,S.layout.layoutBox),St(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=Ae(),this.targetWithTransforms=Ae()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),w2(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):St(this.target,this.layout.layoutBox),Xm(this.target,this.targetDelta)):St(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const S=this.getClosestProjectingParent();S&&!!S.resumingFrom==!!this.resumingFrom&&!S.options.layoutScroll&&S.target&&this.animationProgress!==1?(this.relativeParent=S,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Ae(),this.relativeTargetOrigin=Ae(),Ei(this.relativeTargetOrigin,this.target,S.target),St(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||ql(this.parent.latestValues)||Ym(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var A;const f=this.getLead(),h=!!this.resumingFrom||this!==f;let p=!0;if((this.isProjectionDirty||(A=this.parent)!=null&&A.isProjectionDirty)&&(p=!1),h&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(p=!1),this.resolvedRelativeTargetAt===ze.timestamp&&(p=!1),p)return;const{layout:g,layoutId:y}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(g||y))return;St(this.layoutCorrected,this.layout.layoutBox);const x=this.treeScale.x,w=this.treeScale.y;S2(this.layoutCorrected,this.treeScale,this.path,h),f.layout&&!f.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(f.target=f.layout.layoutBox,f.targetWithTransforms=Ae());const{target:S}=f;if(!S){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Qh(this.prevProjectionDelta.x,this.projectionDelta.x),Qh(this.prevProjectionDelta.y,this.projectionDelta.y)),Si(this.projectionDelta,this.layoutCorrected,S,this.latestValues),(this.treeScale.x!==x||this.treeScale.y!==w||!tp(this.projectionDelta.x,this.prevProjectionDelta.x)||!tp(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",S))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(f=!0){var h;if((h=this.options.visualElement)==null||h.scheduleRender(),f){const p=this.getStack();p&&p.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=wr(),this.projectionDelta=wr(),this.projectionDeltaWithTransform=wr()}setAnimationOrigin(f,h=!1){const p=this.snapshot,g=p?p.latestValues:{},y={...this.latestValues},x=wr();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!h;const w=Ae(),S=p?p.source:void 0,A=this.layout?this.layout.source:void 0,D=S!==A,M=this.getStack(),L=!M||M.members.length<=1,z=!!(D&&!L&&this.options.crossfade===!0&&!this.path.some(mS));this.animationProgress=0;let F;this.mixTargetDelta=Y=>{const U=Y/1e3;ip(x.x,f.x,U),ip(x.y,f.y,U),this.setTargetDelta(x),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Ei(w,this.layout.layoutBox,this.relativeParent.layout.layoutBox),pS(this.relativeTarget,this.relativeTargetOrigin,w,U),F&&Z2(this.relativeTarget,F)&&(this.isProjectionDirty=!1),F||(F=Ae()),St(F,this.relativeTarget)),D&&(this.animationValues=y,H2(y,g,this.latestValues,U,z,L)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=U},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(f){var h,p,g;this.notifyListeners("animationStart"),(h=this.currentAnimation)==null||h.stop(),(g=(p=this.resumingFrom)==null?void 0:p.currentAnimation)==null||g.stop(),this.pendingAnimation&&(En(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Pe.update(()=>{no.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=Tr(0)),this.currentAnimation=B2(this.motionValue,[0,1e3],{...f,velocity:0,isSync:!0,onUpdate:y=>{this.mixTargetDelta(y),f.onUpdate&&f.onUpdate(y)},onStop:()=>{},onComplete:()=>{f.onComplete&&f.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const f=this.getStack();f&&f.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(tS),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const f=this.getLead();let{targetWithTransforms:h,target:p,layout:g,latestValues:y}=f;if(!(!h||!p||!g)){if(this!==f&&this.layout&&g&&l0(this.options.animationType,this.layout.layoutBox,g.layoutBox)){p=this.target||Ae();const x=Ze(this.layout.layoutBox.x);p.x.min=f.target.x.min,p.x.max=p.x.min+x;const w=Ze(this.layout.layoutBox.y);p.y.min=f.target.y.min,p.y.max=p.y.min+w}St(h,p),Er(h,y),Si(this.projectionDeltaWithTransform,this.layoutCorrected,h,y)}}registerSharedNode(f,h){this.sharedNodes.has(f)||this.sharedNodes.set(f,new q2),this.sharedNodes.get(f).add(h);const g=h.options.initialPromotionConfig;h.promote({transition:g?g.transition:void 0,preserveFollowOpacity:g&&g.shouldPreserveFollowOpacity?g.shouldPreserveFollowOpacity(h):void 0})}isLead(){const f=this.getStack();return f?f.lead===this:!0}getLead(){var h;const{layoutId:f}=this.options;return f?((h=this.getStack())==null?void 0:h.lead)||this:this}getPrevLead(){var h;const{layoutId:f}=this.options;return f?(h=this.getStack())==null?void 0:h.prevLead:void 0}getStack(){const{layoutId:f}=this.options;if(f)return this.root.sharedNodes.get(f)}promote({needsReset:f,transition:h,preserveFollowOpacity:p}={}){const g=this.getStack();g&&g.promote(this,p),f&&(this.projectionDelta=void 0,this.needsReset=!0),h&&this.setOptions({transition:h})}relegate(){const f=this.getStack();return f?f.relegate(this):!1}resetSkewAndRotation(){const{visualElement:f}=this.options;if(!f)return;let h=!1;const{latestValues:p}=f;if((p.z||p.rotate||p.rotateX||p.rotateY||p.rotateZ||p.skewX||p.skewY)&&(h=!0),!h)return;const g={};p.z&&Rl("z",f,g,this.animationValues);for(let y=0;y<Nl.length;y++)Rl(`rotate${Nl[y]}`,f,g,this.animationValues),Rl(`skew${Nl[y]}`,f,g,this.animationValues);f.render();for(const y in g)f.setStaticValue(y,g[y]),this.animationValues&&(this.animationValues[y]=g[y]);f.scheduleRender()}getProjectionStyles(f){if(!this.instance||this.isSVG)return;if(!this.isVisible)return eS;const h={visibility:""},p=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,h.opacity="",h.pointerEvents=to(f==null?void 0:f.pointerEvents)||"",h.transform=p?p(this.latestValues,""):"none",h;const g=this.getLead();if(!this.projectionDelta||!this.layout||!g.target){const S={};return this.options.layoutId&&(S.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,S.pointerEvents=to(f==null?void 0:f.pointerEvents)||""),this.hasProjected&&!Bn(this.latestValues)&&(S.transform=p?p({},""):"none",this.hasProjected=!1),S}const y=g.animationValues||g.latestValues;this.applyTransformsToTarget(),h.transform=J2(this.projectionDeltaWithTransform,this.treeScale,y),p&&(h.transform=p(y,h.transform));const{x,y:w}=this.projectionDelta;h.transformOrigin=`${x.origin*100}% ${w.origin*100}% 0`,g.animationValues?h.opacity=g===this?y.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:y.opacityExit:h.opacity=g===this?y.opacity!==void 0?y.opacity:"":y.opacityExit!==void 0?y.opacityExit:0;for(const S in ji){if(y[S]===void 0)continue;const{correct:A,applyTo:D,isCSSVariable:M}=ji[S],L=h.transform==="none"?y[S]:A(y[S],g);if(D){const z=D.length;for(let F=0;F<z;F++)h[D[F]]=L}else M?this.options.visualElement.renderState.vars[S]=L:h[S]=L}return this.options.layoutId&&(h.pointerEvents=g===this?to(f==null?void 0:f.pointerEvents)||"":"none"),h}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(f=>{var h;return(h=f.currentAnimation)==null?void 0:h.stop()}),this.root.nodes.forEach(np),this.root.sharedNodes.clear()}}}function rS(n){n.updateLayout()}function iS(n){var s;const r=((s=n.resumeFrom)==null?void 0:s.snapshot)||n.snapshot;if(n.isLead()&&n.layout&&r&&n.hasListeners("didUpdate")){const{layoutBox:a,measuredBox:u}=n.layout,{animationType:d}=n.options,f=r.source!==n.layout.source;d==="size"?Et(x=>{const w=f?r.measuredBox[x]:r.layoutBox[x],S=Ze(w);w.min=a[x].min,w.max=w.min+S}):l0(d,r.layoutBox,a)&&Et(x=>{const w=f?r.measuredBox[x]:r.layoutBox[x],S=Ze(a[x]);w.max=w.min+S,n.relativeTarget&&!n.currentAnimation&&(n.isProjectionDirty=!0,n.relativeTarget[x].max=n.relativeTarget[x].min+S)});const h=wr();Si(h,a,r.layoutBox);const p=wr();f?Si(p,n.applyTransform(u,!0),r.measuredBox):Si(p,a,r.layoutBox);const g=!i0(h);let y=!1;if(!n.resumeFrom){const x=n.getClosestProjectingParent();if(x&&!x.resumeFrom){const{snapshot:w,layout:S}=x;if(w&&S){const A=Ae();Ei(A,r.layoutBox,w.layoutBox);const D=Ae();Ei(D,a,S.layoutBox),s0(A,D)||(y=!0),x.options.layoutRoot&&(n.relativeTarget=D,n.relativeTargetOrigin=A,n.relativeParent=x)}}}n.notifyListeners("didUpdate",{layout:a,snapshot:r,delta:p,layoutDelta:h,hasLayoutChanged:g,hasRelativeLayoutChanged:y})}else if(n.isLead()){const{onExitComplete:a}=n.options;a&&a()}n.options.transition=void 0}function sS(n){n.parent&&(n.isProjecting()||(n.isProjectionDirty=n.parent.isProjectionDirty),n.isSharedProjectionDirty||(n.isSharedProjectionDirty=!!(n.isProjectionDirty||n.parent.isProjectionDirty||n.parent.isSharedProjectionDirty)),n.isTransformDirty||(n.isTransformDirty=n.parent.isTransformDirty))}function oS(n){n.isProjectionDirty=n.isSharedProjectionDirty=n.isTransformDirty=!1}function aS(n){n.clearSnapshot()}function np(n){n.clearMeasurements()}function lS(n){n.isLayoutDirty=!1}function uS(n){const{visualElement:r}=n.options;r&&r.getProps().onBeforeLayoutMeasure&&r.notify("BeforeLayoutMeasure"),n.resetTransform()}function rp(n){n.finishAnimation(),n.targetDelta=n.relativeTarget=n.target=void 0,n.isProjectionDirty=!0}function cS(n){n.resolveTargetDelta()}function dS(n){n.calcProjection()}function fS(n){n.resetSkewAndRotation()}function hS(n){n.removeLeadSnapshot()}function ip(n,r,s){n.translate=ke(r.translate,0,s),n.scale=ke(r.scale,1,s),n.origin=r.origin,n.originPoint=r.originPoint}function sp(n,r,s,a){n.min=ke(r.min,s.min,a),n.max=ke(r.max,s.max,a)}function pS(n,r,s,a){sp(n.x,r.x,s.x,a),sp(n.y,r.y,s.y,a)}function mS(n){return n.animationValues&&n.animationValues.opacityExit!==void 0}const gS={duration:.45,ease:[.4,0,.1,1]},op=n=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(n),ap=op("applewebkit/")&&!op("chrome/")?Math.round:Ct;function lp(n){n.min=ap(n.min),n.max=ap(n.max)}function yS(n){lp(n.x),lp(n.y)}function l0(n,r,s){return n==="position"||n==="preserve-aspect"&&!x2(ep(r),ep(s),.2)}function vS(n){var r;return n!==n.root&&((r=n.scroll)==null?void 0:r.wasRoot)}const xS=a0({attachResizeListener:(n,r)=>Ri(n,"resize",r),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Ml={current:void 0},u0=a0({measureScroll:n=>({x:n.scrollLeft,y:n.scrollTop}),defaultParent:()=>{if(!Ml.current){const n=new xS({});n.mount(window),n.setOptions({layoutScroll:!0}),Ml.current=n}return Ml.current},resetTransform:(n,r)=>{n.style.transform=r!==void 0?r:"none"},checkIsScrollRoot:n=>window.getComputedStyle(n).position==="fixed"}),wS={pan:{Feature:b2},drag:{Feature:_2,ProjectionNode:u0,MeasureLayout:t0}};function up(n,r,s){const{props:a}=n;n.animationState&&a.whileHover&&n.animationState.setActive("whileHover",s==="Start");const u="onHover"+s,d=a[u];d&&Pe.postRender(()=>d(r,Vi(r)))}class SS extends kn{mount(){const{current:r}=this.node;r&&(this.unmount=Gx(r,(s,a)=>(up(this.node,a,"Start"),u=>up(this.node,u,"End"))))}unmount(){}}class ES extends kn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let r=!1;try{r=this.node.current.matches(":focus-visible")}catch{r=!0}!r||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Ai(Ri(this.node.current,"focus",()=>this.onFocus()),Ri(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function cp(n,r,s){const{props:a}=n;if(n.current instanceof HTMLButtonElement&&n.current.disabled)return;n.animationState&&a.whileTap&&n.animationState.setActive("whileTap",s==="Start");const u="onTap"+(s==="End"?"":s),d=a[u];d&&Pe.postRender(()=>d(r,Vi(r)))}class CS extends kn{mount(){const{current:r}=this.node;r&&(this.unmount=qx(r,(s,a)=>(cp(this.node,a,"Start"),(u,{success:d})=>cp(this.node,u,d?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const tu=new WeakMap,Al=new WeakMap,TS=n=>{const r=tu.get(n.target);r&&r(n)},kS=n=>{n.forEach(TS)};function PS({root:n,...r}){const s=n||document;Al.has(s)||Al.set(s,{});const a=Al.get(s),u=JSON.stringify(r);return a[u]||(a[u]=new IntersectionObserver(kS,{root:n,...r})),a[u]}function jS(n,r,s){const a=PS(r);return tu.set(n,s),a.observe(n),()=>{tu.delete(n),a.unobserve(n)}}const NS={some:0,all:1};class RS extends kn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:r={}}=this.node.getProps(),{root:s,margin:a,amount:u="some",once:d}=r,f={root:s?s.current:void 0,rootMargin:a,threshold:typeof u=="number"?u:NS[u]},h=p=>{const{isIntersecting:g}=p;if(this.isInView===g||(this.isInView=g,d&&!g&&this.hasEnteredView))return;g&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",g);const{onViewportEnter:y,onViewportLeave:x}=this.node.getProps(),w=g?y:x;w&&w(p)};return jS(this.node.current,f,h)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:r,prevProps:s}=this.node;["amount","margin","root"].some(MS(r,s))&&this.startObserver()}unmount(){}}function MS({viewport:n={}},{viewport:r={}}={}){return s=>n[s]!==r[s]}const AS={inView:{Feature:RS},tap:{Feature:CS},focus:{Feature:ES},hover:{Feature:SS}},LS={layout:{ProjectionNode:u0,MeasureLayout:t0}},nu={current:null},c0={current:!1};function DS(){if(c0.current=!0,!!au)if(window.matchMedia){const n=window.matchMedia("(prefers-reduced-motion)"),r=()=>nu.current=n.matches;n.addListener(r),r()}else nu.current=!1}const VS=new WeakMap;function _S(n,r,s){for(const a in r){const u=r[a],d=s[a];if(Ke(u))n.addValue(a,u);else if(Ke(d))n.addValue(a,Tr(u,{owner:n}));else if(d!==u)if(n.hasValue(a)){const f=n.getValue(a);f.liveStyle===!0?f.jump(u):f.hasAnimated||f.set(u)}else{const f=n.getStaticValue(a);n.addValue(a,Tr(f!==void 0?f:u,{owner:n}))}}for(const a in s)r[a]===void 0&&n.removeValue(a);return r}const dp=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class bS{scrapeMotionValuesFromProps(r,s,a){return{}}constructor({parent:r,props:s,presenceContext:a,reducedMotionConfig:u,blockInitialAnimation:d,visualState:f},h={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Tu,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const w=at.now();this.renderScheduledAt<w&&(this.renderScheduledAt=w,Pe.render(this.render,!1,!0))};const{latestValues:p,renderState:g}=f;this.latestValues=p,this.baseTarget={...p},this.initialValues=s.initial?{...p}:{},this.renderState=g,this.parent=r,this.props=s,this.presenceContext=a,this.depth=r?r.depth+1:0,this.reducedMotionConfig=u,this.options=h,this.blockInitialAnimation=!!d,this.isControllingVariants=So(s),this.isVariantNode=Am(s),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(r&&r.current);const{willChange:y,...x}=this.scrapeMotionValuesFromProps(s,{},this);for(const w in x){const S=x[w];p[w]!==void 0&&Ke(S)&&S.set(p[w],!1)}}mount(r){this.current=r,VS.set(r,this),this.projection&&!this.projection.instance&&this.projection.mount(r),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((s,a)=>this.bindToMotionValue(a,s)),c0.current||DS(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:nu.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),En(this.notifyUpdate),En(this.render),this.valueSubscriptions.forEach(r=>r()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const r in this.events)this.events[r].clear();for(const r in this.features){const s=this.features[r];s&&(s.unmount(),s.isMounted=!1)}this.current=null}bindToMotionValue(r,s){this.valueSubscriptions.has(r)&&this.valueSubscriptions.get(r)();const a=Nr.has(r);a&&this.onBindTransform&&this.onBindTransform();const u=s.on("change",h=>{this.latestValues[r]=h,this.props.onUpdate&&Pe.preRender(this.notifyUpdate),a&&this.projection&&(this.projection.isTransformDirty=!0)}),d=s.on("renderRequest",this.scheduleRender);let f;window.MotionCheckAppearSync&&(f=window.MotionCheckAppearSync(this,r,s)),this.valueSubscriptions.set(r,()=>{u(),d(),f&&f(),s.owner&&s.stop()})}sortNodePosition(r){return!this.current||!this.sortInstanceNodePosition||this.type!==r.type?0:this.sortInstanceNodePosition(this.current,r.current)}updateFeatures(){let r="animation";for(r in kr){const s=kr[r];if(!s)continue;const{isEnabled:a,Feature:u}=s;if(!this.features[r]&&u&&a(this.props)&&(this.features[r]=new u(this)),this.features[r]){const d=this.features[r];d.isMounted?d.update():(d.mount(),d.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):Ae()}getStaticValue(r){return this.latestValues[r]}setStaticValue(r,s){this.latestValues[r]=s}update(r,s){(r.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=r,this.prevPresenceContext=this.presenceContext,this.presenceContext=s;for(let a=0;a<dp.length;a++){const u=dp[a];this.propEventSubscriptions[u]&&(this.propEventSubscriptions[u](),delete this.propEventSubscriptions[u]);const d="on"+u,f=r[d];f&&(this.propEventSubscriptions[u]=this.on(u,f))}this.prevMotionValues=_S(this,this.scrapeMotionValuesFromProps(r,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(r){return this.props.variants?this.props.variants[r]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(r){const s=this.getClosestVariantNode();if(s)return s.variantChildren&&s.variantChildren.add(r),()=>s.variantChildren.delete(r)}addValue(r,s){const a=this.values.get(r);s!==a&&(a&&this.removeValue(r),this.bindToMotionValue(r,s),this.values.set(r,s),this.latestValues[r]=s.get())}removeValue(r){this.values.delete(r);const s=this.valueSubscriptions.get(r);s&&(s(),this.valueSubscriptions.delete(r)),delete this.latestValues[r],this.removeValueFromRenderState(r,this.renderState)}hasValue(r){return this.values.has(r)}getValue(r,s){if(this.props.values&&this.props.values[r])return this.props.values[r];let a=this.values.get(r);return a===void 0&&s!==void 0&&(a=Tr(s===null?void 0:s,{owner:this}),this.addValue(r,a)),a}readValue(r,s){let a=this.latestValues[r]!==void 0||!this.current?this.latestValues[r]:this.getBaseTargetFromProps(this.props,r)??this.readValueFromInstance(this.current,r,this.options);return a!=null&&(typeof a=="string"&&(Ip(a)||zp(a))?a=parseFloat(a):!tw(a)&&Cn.test(s)&&(a=Em(r,s)),this.setBaseTarget(r,Ke(a)?a.get():a)),Ke(a)?a.get():a}setBaseTarget(r,s){this.baseTarget[r]=s}getBaseTarget(r){var d;const{initial:s}=this.props;let a;if(typeof s=="string"||typeof s=="object"){const f=Ou(this.props,s,(d=this.presenceContext)==null?void 0:d.custom);f&&(a=f[r])}if(s&&a!==void 0)return a;const u=this.getBaseTargetFromProps(this.props,r);return u!==void 0&&!Ke(u)?u:this.initialValues[r]!==void 0&&a===void 0?void 0:this.baseTarget[r]}on(r,s){return this.events[r]||(this.events[r]=new fu),this.events[r].add(s)}notify(r,...s){this.events[r]&&this.events[r].notify(...s)}}class d0 extends bS{constructor(){super(...arguments),this.KeyframeResolver=$x}sortInstanceNodePosition(r,s){return r.compareDocumentPosition(s)&2?1:-1}getBaseTargetFromProps(r,s){return r.style?r.style[s]:void 0}removeValueFromRenderState(r,{vars:s,style:a}){delete s[r],delete a[r]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:r}=this.props;Ke(r)&&(this.childSubscription=r.on("change",s=>{this.current&&(this.current.textContent=`${s}`)}))}}function f0(n,{style:r,vars:s},a,u){Object.assign(n.style,r,u&&u.getProjectionStyles(a));for(const d in s)n.style.setProperty(d,s[d])}function OS(n){return window.getComputedStyle(n)}class FS extends d0{constructor(){super(...arguments),this.type="html",this.renderInstance=f0}readValueFromInstance(r,s){var a;if(Nr.has(s))return(a=this.projection)!=null&&a.isProjecting?Ul(s):lx(r,s);{const u=OS(r),d=(mu(s)?u.getPropertyValue(s):u[s])||0;return typeof d=="string"?d.trim():d}}measureInstanceViewportBox(r,{transformPagePoint:s}){return Zm(r,s)}build(r,s,a){Vu(r,s,a.transformTemplate)}scrapeMotionValuesFromProps(r,s,a){return Fu(r,s,a)}}const h0=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function IS(n,r,s,a){f0(n,r,void 0,a);for(const u in r.attrs)n.setAttribute(h0.has(u)?u:Du(u),r.attrs[u])}class BS extends d0{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Ae}getBaseTargetFromProps(r,s){return r[s]}readValueFromInstance(r,s){if(Nr.has(s)){const a=Sm(s);return a&&a.default||0}return s=h0.has(s)?s:Du(s),r.getAttribute(s)}scrapeMotionValuesFromProps(r,s,a){return zm(r,s,a)}build(r,s,a){Om(r,s,this.isSVGTag,a.transformTemplate,a.style)}renderInstance(r,s,a,u){IS(r,s,a,u)}mount(r){this.isSVGTag=Im(r.tagName),super.mount(r)}}const zS=(n,r)=>bu(n)?new BS(r):new FS(r,{allowProjection:n!==C.Fragment}),US=Iw({...d2,...AS,...wS,...LS},zS),le=cw(US);function $S({title:n,titleId:r,...s},a){return C.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},s),n?C.createElement("title",{id:r},n):null,C.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))}const WS=C.forwardRef($S);function HS({title:n,titleId:r,...s},a){return C.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},s),n?C.createElement("title",{id:r},n):null,C.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))}const KS=C.forwardRef(HS);function QS({title:n,titleId:r,...s},a){return C.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},s),n?C.createElement("title",{id:r},n):null,C.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))}const fp=C.forwardRef(QS);function GS({title:n,titleId:r,...s},a){return C.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},s),n?C.createElement("title",{id:r},n):null,C.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const YS=C.forwardRef(GS);function XS({title:n,titleId:r,...s},a){return C.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},s),n?C.createElement("title",{id:r},n):null,C.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const p0=C.forwardRef(XS);function ZS({title:n,titleId:r,...s},a){return C.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},s),n?C.createElement("title",{id:r},n):null,C.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const hp=C.forwardRef(ZS);function qS({title:n,titleId:r,...s},a){return C.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},s),n?C.createElement("title",{id:r},n):null,C.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),C.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const m0=C.forwardRef(qS);function JS({title:n,titleId:r,...s},a){return C.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},s),n?C.createElement("title",{id:r},n):null,C.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z"}))}const e5=C.forwardRef(JS);function t5({title:n,titleId:r,...s},a){return C.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},s),n?C.createElement("title",{id:r},n):null,C.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}const pp=C.forwardRef(t5);function n5({title:n,titleId:r,...s},a){return C.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},s),n?C.createElement("title",{id:r},n):null,C.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}),C.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"}))}const ro=C.forwardRef(n5);function r5({title:n,titleId:r,...s},a){return C.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},s),n?C.createElement("title",{id:r},n):null,C.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))}const i5=C.forwardRef(r5);function s5({title:n,titleId:r,...s},a){return C.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},s),n?C.createElement("title",{id:r},n):null,C.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))}const mp=C.forwardRef(s5);function o5({title:n,titleId:r,...s},a){return C.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},s),n?C.createElement("title",{id:r},n):null,C.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 10.5V6a3.75 3.75 0 1 0-7.5 0v4.5m11.356-1.993 1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 0 1-1.12-1.243l1.264-12A1.125 1.125 0 0 1 5.513 7.5h12.974c.576 0 1.059.435 1.119 1.007ZM8.625 10.5a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm7.5 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"}))}const gp=C.forwardRef(o5);function a5({title:n,titleId:r,...s},a){return C.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},s),n?C.createElement("title",{id:r},n):null,C.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 0 0-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 0 0-16.536-1.84M7.5 14.25 5.106 5.272M6 20.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Zm12.75 0a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z"}))}const l5=C.forwardRef(a5);function u5({title:n,titleId:r,...s},a){return C.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},s),n?C.createElement("title",{id:r},n):null,C.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"}))}const g0=C.forwardRef(u5);function c5({title:n,titleId:r,...s},a){return C.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},s),n?C.createElement("title",{id:r},n):null,C.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))}const d5=C.forwardRef(c5);function f5({title:n,titleId:r,...s},a){return C.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},s),n?C.createElement("title",{id:r},n):null,C.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))}const yp=C.forwardRef(f5);function h5({title:n,titleId:r,...s},a){return C.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},s),n?C.createElement("title",{id:r},n):null,C.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}const p5=C.forwardRef(h5);function m5({title:n,titleId:r,...s},a){return C.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},s),n?C.createElement("title",{id:r},n):null,C.createElement("path",{d:"m11.645 20.91-.007-.003-.022-.012a15.247 15.247 0 0 1-.383-.218 25.18 25.18 0 0 1-4.244-3.17C4.688 15.36 2.25 12.174 2.25 8.25 2.25 5.322 4.714 3 7.688 3A5.5 5.5 0 0 1 12 5.052 5.5 5.5 0 0 1 16.313 3c2.973 0 5.437 2.322 5.437 5.25 0 3.925-2.438 7.111-4.739 9.256a25.175 25.175 0 0 1-4.244 3.17 15.247 15.247 0 0 1-.383.219l-.022.012-.007.004-.003.001a.752.752 0 0 1-.704 0l-.003-.001Z"}))}const g5=C.forwardRef(m5);function y5({title:n,titleId:r,...s},a){return C.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},s),n?C.createElement("title",{id:r},n):null,C.createElement("path",{d:"M2.25 2.25a.75.75 0 0 0 0 1.5h1.386c.17 0 .318.114.362.278l2.558 9.592a3.752 3.752 0 0 0-2.806 3.63c0 .414.336.75.75.75h15.75a.75.75 0 0 0 0-1.5H5.378A2.25 2.25 0 0 1 7.5 15h11.218a.75.75 0 0 0 .674-.421 60.358 60.358 0 0 0 2.96-7.228.75.75 0 0 0-.525-.965A60.864 60.864 0 0 0 5.68 4.509l-.232-.867A1.875 1.875 0 0 0 3.636 2.25H2.25ZM3.75 20.25a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0ZM16.5 20.25a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Z"}))}const v5=C.forwardRef(y5);function x5({title:n,titleId:r,...s},a){return C.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},s),n?C.createElement("title",{id:r},n):null,C.createElement("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.006 5.404.434c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.434 2.082-5.005Z",clipRule:"evenodd"}))}const vp=C.forwardRef(x5),w5=()=>{const{state:n,dispatch:r,switchRole:s,logout:a}=yo(),[u,d]=C.useState(!1),[f,h]=C.useState(!1),p=n.cart.reduce((y,x)=>y+x.quantity,0),g=()=>{const y=n.userRole==="customer"?"vendor":"customer";s(y),h(!1)};return v.jsxs(le.header,{initial:{y:-100},animate:{y:0},className:"sticky top-0 z-50 bg-white shadow-lg border-b border-gray-100",children:[v.jsx("div",{className:"bg-gradient-to-r from-blue-600 via-purple-600 to-orange-500 h-1"}),v.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[v.jsxs("div",{className:"flex items-center justify-between h-16",children:[v.jsxs(le.div,{className:"flex items-center space-x-3",whileHover:{scale:1.05},whileTap:{scale:.95},children:[v.jsxs("div",{className:"relative",children:[v.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg",children:v.jsx(ro,{className:"w-6 h-6 text-white"})}),v.jsx("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white animate-pulse"})]}),v.jsxs("div",{children:[v.jsx("h1",{className:"text-xl font-bold bg-gradient-to-r from-blue-600 to-orange-500 bg-clip-text text-transparent",children:"MarketPlace Local"}),v.jsx("p",{className:"text-xs text-gray-500",children:"Votre proximité digitale"})]})]}),v.jsx("div",{className:"hidden md:flex flex-1 max-w-md mx-8",children:v.jsxs("div",{className:"relative w-full",children:[v.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:v.jsx(pp,{className:"h-5 w-5 text-gray-400"})}),v.jsx("input",{type:"text",placeholder:"Rechercher produits, vendeurs...",value:n.searchQuery,onChange:y=>r({type:"SET_SEARCH_QUERY",payload:y.target.value}),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-xl bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 hover:bg-white"})]})}),v.jsxs("div",{className:"flex items-center space-x-4",children:[v.jsx(le.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:g,className:`hidden sm:flex items-center px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 ${n.userRole==="vendor"?"bg-purple-100 text-purple-700 hover:bg-purple-200":"bg-blue-100 text-blue-700 hover:bg-blue-200"}`,children:n.userRole==="customer"?"👤 Client":"🏪 Vendeur"}),v.jsxs(le.button,{whileHover:{scale:1.1},whileTap:{scale:.9},className:"relative p-2 text-gray-600 hover:text-blue-600 transition-colors duration-200",children:[v.jsx(KS,{className:"w-6 h-6"}),v.jsx("span",{className:"absolute top-0 right-0 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center",children:"3"})]}),n.userRole==="customer"&&v.jsxs(le.button,{whileHover:{scale:1.1},whileTap:{scale:.9},className:"relative p-2 text-gray-600 hover:text-orange-600 transition-colors duration-200",children:[p>0?v.jsx(v5,{className:"w-6 h-6 text-orange-500"}):v.jsx(l5,{className:"w-6 h-6"}),p>0&&v.jsx(le.span,{initial:{scale:0},animate:{scale:1},className:"absolute -top-1 -right-1 w-5 h-5 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center font-medium",children:p})]}),v.jsxs("div",{className:"relative",children:[v.jsx(le.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>h(!f),className:"flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50 transition-all duration-200",children:n.user?v.jsxs("div",{className:"flex items-center space-x-2",children:[v.jsx("img",{src:n.user.avatar||"/images/default-avatar.jpg",alt:n.user.name,className:"w-8 h-8 rounded-full object-cover border-2 border-gray-200"}),v.jsx("span",{className:"hidden sm:block text-sm font-medium text-gray-700",children:n.user.name.split(" ")[0]})]}):v.jsxs("div",{className:"flex items-center space-x-2",children:[v.jsx(d5,{className:"w-6 h-6 text-gray-600"}),v.jsx("span",{className:"hidden sm:block text-sm font-medium text-gray-700",children:"Connexion"})]})}),v.jsx(Gl,{children:f&&v.jsx(le.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50",children:n.user?v.jsxs(v.Fragment,{children:[v.jsxs("div",{className:"px-4 py-2 border-b border-gray-100",children:[v.jsx("p",{className:"text-sm font-medium text-gray-900",children:n.user.name}),v.jsx("p",{className:"text-xs text-gray-500",children:n.user.email})]}),v.jsx("button",{className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50",children:"Mon profil"}),v.jsx("button",{className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50",children:"Mes commandes"}),v.jsx("button",{className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50",children:"Mes favoris"}),v.jsx("hr",{className:"my-2"}),v.jsx("button",{onClick:a,className:"block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50",children:"Déconnexion"})]}):v.jsxs(v.Fragment,{children:[v.jsx("button",{className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50",children:"Se connecter"}),v.jsx("button",{className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50",children:"S'inscrire"})]})})})]}),v.jsx(le.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>d(!u),className:"md:hidden p-2 text-gray-600 hover:text-blue-600 transition-colors duration-200",children:u?v.jsx(p5,{className:"w-6 h-6"}):v.jsx(WS,{className:"w-6 h-6"})})]})]}),v.jsx(Gl,{children:u&&v.jsx(le.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"md:hidden border-t border-gray-200 bg-white",children:v.jsxs("div",{className:"px-4 py-4 space-y-4",children:[v.jsxs("div",{className:"relative",children:[v.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:v.jsx(pp,{className:"h-5 w-5 text-gray-400"})}),v.jsx("input",{type:"text",placeholder:"Rechercher...",value:n.searchQuery,onChange:y=>r({type:"SET_SEARCH_QUERY",payload:y.target.value}),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),v.jsx("button",{onClick:g,className:`w-full text-left px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${n.userRole==="vendor"?"bg-purple-100 text-purple-700":"bg-blue-100 text-blue-700"}`,children:n.userRole==="customer"?"👤 Mode Client":"🏪 Mode Vendeur"})]})})})]})]})},xp=()=>{var x;const{state:n,dispatch:r}=yo(),[s,a]=C.useState(null),[u,d]=C.useState({lat:14.6928,lng:-17.4467}),[f,h]=C.useState(new Set),p=n.vendors.filter(w=>{const S=!n.searchQuery||w.name.toLowerCase().includes(n.searchQuery.toLowerCase())||w.business.toLowerCase().includes(n.searchQuery.toLowerCase())||w.specialties.some(D=>D.toLowerCase().includes(n.searchQuery.toLowerCase())),A=!n.selectedCategory||w.category===n.selectedCategory;return S&&A}),g=w=>{const S=new Set(f);S.has(w)?S.delete(w):S.add(w),h(S)},y=w=>{const S=u.lat,A=u.lng,D=w.location.lat,M=w.location.lng,L=Math.sqrt(Math.pow(D-S,2)+Math.pow(M-A,2))*111;return Math.round(L*10)/10};return v.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-orange-50",children:v.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[v.jsxs(le.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center mb-12",children:[v.jsx("h1",{className:"text-4xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-orange-500 bg-clip-text text-transparent mb-4",children:"Découvrez Votre Quartier"}),v.jsxs("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto mb-8",children:["Plus de ",n.vendors.length," vendeurs locaux vous attendent avec leurs produits authentiques et services de qualité"]}),v.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto",children:[{label:"Vendeurs actifs",value:n.vendors.length,icon:"🏪",color:"from-blue-500 to-blue-600"},{label:"Produits disponibles",value:n.products.length,icon:"📦",color:"from-green-500 to-green-600"},{label:"Commandes livrées",value:"2,847",icon:"🚚",color:"from-orange-500 to-orange-600"},{label:"Clients satisfaits",value:"5,623",icon:"😊",color:"from-purple-500 to-purple-600"}].map((w,S)=>v.jsxs(le.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:S*.1},className:"bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300",children:[v.jsx("div",{className:`text-3xl mb-2 bg-gradient-to-r ${w.color} w-12 h-12 rounded-xl flex items-center justify-center text-white mx-auto`,children:w.icon}),v.jsx("div",{className:"text-2xl font-bold text-gray-900",children:w.value}),v.jsx("div",{className:"text-sm text-gray-600",children:w.label})]},S))})]}),v.jsx(le.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"mb-8",children:v.jsxs("div",{className:"flex flex-wrap gap-3 justify-center",children:[v.jsx(le.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>r({type:"SET_SELECTED_CATEGORY",payload:""}),className:`px-6 py-3 rounded-full font-medium transition-all duration-200 ${n.selectedCategory?"bg-white text-gray-700 border border-gray-300 hover:border-blue-300":"bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg"}`,children:"Toutes catégories"}),(x=n.config)==null?void 0:x.categories.map(w=>v.jsxs(le.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>r({type:"SET_SELECTED_CATEGORY",payload:w.name}),className:`px-6 py-3 rounded-full font-medium transition-all duration-200 flex items-center space-x-2 ${n.selectedCategory===w.name?"text-white shadow-lg":"bg-white text-gray-700 border border-gray-300 hover:border-blue-300"}`,style:n.selectedCategory===w.name?{background:`linear-gradient(135deg, ${w.color}, ${w.color}dd)`}:{},children:[v.jsx("span",{className:"text-lg",children:w.icon}),v.jsx("span",{children:w.name})]},w.id))]})}),v.jsxs("div",{className:"grid lg:grid-cols-2 gap-8",children:[v.jsxs(le.div,{initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{delay:.3},className:"bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-200",children:[v.jsxs("div",{className:"h-16 bg-gradient-to-r from-blue-500 to-orange-500 flex items-center px-6",children:[v.jsx(ro,{className:"w-6 h-6 text-white mr-3"}),v.jsx("h3",{className:"text-white font-semibold text-lg",children:"Carte Interactive - Dakar"})]}),v.jsxs("div",{className:"relative h-96 bg-gradient-to-br from-blue-100 to-green-100 overflow-hidden",children:[v.jsxs("div",{className:"absolute inset-0 opacity-20",children:[v.jsx("div",{className:"absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-300 to-green-300"}),v.jsx("div",{className:"absolute top-1/4 left-1/4 w-32 h-32 bg-blue-400 rounded-full opacity-30 animate-pulse"}),v.jsx("div",{className:"absolute bottom-1/4 right-1/4 w-24 h-24 bg-green-400 rounded-full opacity-30 animate-pulse"})]}),p.map((w,S)=>{const A=20+S%3*30,D=15+Math.floor(S/3)*25;return v.jsxs(le.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.5+S*.1},className:"absolute cursor-pointer",style:{left:`${A}%`,top:`${D}%`,transform:"translate(-50%, -50%)"},onClick:()=>a(w),children:[v.jsx(le.div,{whileHover:{scale:1.2},whileTap:{scale:.9},className:`w-8 h-8 rounded-full border-3 border-white shadow-lg flex items-center justify-center text-white font-bold text-sm ${(s==null?void 0:s.id)===w.id?"bg-orange-500 ring-4 ring-orange-200":"bg-blue-500 hover:bg-blue-600"}`,children:w.business.charAt(0)}),v.jsx(Gl,{children:(s==null?void 0:s.id)===w.id&&v.jsxs(le.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},className:"absolute top-10 left-1/2 transform -translate-x-1/2 bg-white rounded-lg shadow-xl p-3 min-w-48 z-10 border",children:[v.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[v.jsx("img",{src:w.avatar,alt:w.name,className:"w-8 h-8 rounded-full object-cover"}),v.jsxs("div",{children:[v.jsx("p",{className:"font-semibold text-sm text-gray-900",children:w.business}),v.jsx("p",{className:"text-xs text-gray-600",children:w.name})]})]}),v.jsxs("div",{className:"flex items-center space-x-1 mb-1",children:[v.jsx(vp,{className:"w-4 h-4 text-yellow-400"}),v.jsx("span",{className:"text-sm font-medium",children:w.rating}),v.jsxs("span",{className:"text-xs text-gray-500",children:["(",w.reviews,")"]})]}),v.jsxs("p",{className:"text-xs text-gray-600 mb-2",children:[y(w)," km"]}),v.jsxs("div",{className:"flex space-x-2",children:[v.jsx("button",{className:"text-xs bg-blue-500 text-white px-2 py-1 rounded",children:"Voir"}),v.jsx("button",{className:"text-xs bg-green-500 text-white px-2 py-1 rounded",children:"Appeler"})]})]})})]},w.id)}),v.jsx(le.button,{whileHover:{scale:1.1},whileTap:{scale:.9},className:"absolute bottom-4 right-4 bg-white rounded-full p-3 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-200",children:v.jsx(ro,{className:"w-5 h-5 text-blue-500"})})]})]}),v.jsxs(le.div,{initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{delay:.4},className:"space-y-4",children:[v.jsx("div",{className:"flex items-center justify-between mb-6",children:v.jsxs("h3",{className:"text-2xl font-bold text-gray-900",children:["Vendeurs à proximité",v.jsxs("span",{className:"text-sm font-normal text-gray-500 ml-2",children:["(",p.length," résultats)"]})]})}),v.jsx("div",{className:"space-y-4 max-h-96 overflow-y-auto pr-2 custom-scrollbar",children:p.map((w,S)=>v.jsx(le.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5+S*.1},whileHover:{y:-2},className:"bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 cursor-pointer",onClick:()=>a(w),children:v.jsxs("div",{className:"flex items-start space-x-4",children:[v.jsxs("div",{className:"relative",children:[v.jsx("img",{src:w.avatar,alt:w.name,className:"w-16 h-16 rounded-full object-cover border-4 border-white shadow-md"}),v.jsx("div",{className:`absolute -bottom-1 -right-1 w-5 h-5 rounded-full border-2 border-white ${w.isOnline?"bg-green-400":"bg-gray-400"}`})]}),v.jsxs("div",{className:"flex-1 min-w-0",children:[v.jsxs("div",{className:"flex items-start justify-between",children:[v.jsxs("div",{children:[v.jsx("h4",{className:"font-bold text-lg text-gray-900 mb-1",children:w.business}),v.jsx("p",{className:"text-sm text-gray-600 mb-2",children:w.name})]}),v.jsx(le.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:A=>{A.stopPropagation(),g(w.id)},className:"p-2 rounded-full hover:bg-gray-100 transition-colors duration-200",children:f.has(w.id)?v.jsx(g5,{className:"w-5 h-5 text-red-500"}):v.jsx(e5,{className:"w-5 h-5 text-gray-400"})})]}),v.jsxs("div",{className:"flex items-center space-x-4 mb-3",children:[v.jsxs("div",{className:"flex items-center",children:[v.jsx(vp,{className:"w-4 h-4 text-yellow-400 mr-1"}),v.jsx("span",{className:"font-medium text-sm",children:w.rating}),v.jsxs("span",{className:"text-xs text-gray-500 ml-1",children:["(",w.reviews," avis)"]})]}),v.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[v.jsx(ro,{className:"w-4 h-4 mr-1"}),v.jsxs("span",{children:[y(w)," km"]})]})]}),v.jsxs("div",{className:"flex flex-wrap gap-2 mb-3",children:[w.specialties.slice(0,2).map((A,D)=>v.jsx("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full",children:A},D)),w.specialties.length>2&&v.jsxs("span",{className:"px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full",children:["+",w.specialties.length-2]})]}),v.jsxs("div",{className:"flex items-center justify-between",children:[v.jsxs("div",{className:"flex items-center space-x-4 text-xs text-gray-500",children:[v.jsxs("div",{className:"flex items-center",children:[v.jsx(p0,{className:"w-3 h-3 mr-1"}),v.jsx("span",{children:"Ouvert"})]}),w.deliveryRadius>0&&v.jsxs("div",{className:"flex items-center",children:[v.jsx(g0,{className:"w-3 h-3 mr-1"}),v.jsxs("span",{children:[w.deliveryFee," FCFA"]})]})]}),v.jsxs("div",{className:"flex space-x-2",children:[v.jsxs(le.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:A=>{A.stopPropagation()},className:"flex items-center px-3 py-1.5 bg-green-500 text-white text-xs rounded-lg hover:bg-green-600 transition-colors duration-200",children:[v.jsx(i5,{className:"w-3 h-3 mr-1"}),"Appeler"]}),v.jsxs(le.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"flex items-center px-3 py-1.5 bg-blue-500 text-white text-xs rounded-lg hover:bg-blue-600 transition-colors duration-200",children:[v.jsx(m0,{className:"w-3 h-3 mr-1"}),"Voir"]})]})]})]})]})},w.id))})]})]})]})})},wp=()=>{const{state:n,getVendorOrders:r,getProductsByVendor:s}=yo(),[a,u]=C.useState("overview"),d=n.vendors[0],f=r((d==null?void 0:d.id)||"1"),h=s((d==null?void 0:d.id)||"1"),p={totalOrders:f.length,totalRevenue:f.reduce((S,A)=>S+A.total,0),activeProducts:h.filter(S=>S.isAvailable).length,averageRating:(d==null?void 0:d.rating)||0,pendingOrders:f.filter(S=>S.status==="pending"||S.status==="confirmed").length,completedOrders:f.filter(S=>S.status==="delivered"||S.status==="completed").length},g=S=>({pending:"bg-yellow-100 text-yellow-800",confirmed:"bg-blue-100 text-blue-800",prepared:"bg-purple-100 text-purple-800",in_production:"bg-indigo-100 text-indigo-800",shipped:"bg-cyan-100 text-cyan-800",in_transit:"bg-cyan-100 text-cyan-800",delivered:"bg-green-100 text-green-800",completed:"bg-green-100 text-green-800",cancelled:"bg-red-100 text-red-800"})[S]||"bg-gray-100 text-gray-800",y=S=>({pending:"En attente",confirmed:"Confirmée",prepared:"Préparée",in_production:"En production",shipped:"Expédiée",in_transit:"En transit",delivered:"Livrée",completed:"Terminée",cancelled:"Annulée"})[S]||S,x=S=>new Intl.NumberFormat("fr-FR",{style:"currency",currency:"XOF",minimumFractionDigits:0}).format(S),w=({tab:S,label:A,icon:D})=>v.jsxs(le.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>u(S),className:`flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-200 ${a===S?"bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg":"bg-white text-gray-700 border border-gray-200 hover:border-blue-300 hover:shadow-md"}`,children:[D,v.jsx("span",{children:A})]});return v.jsx("div",{className:"min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50",children:v.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[v.jsxs(le.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"mb-8",children:[v.jsxs("div",{className:"flex items-center justify-between mb-6",children:[v.jsxs("div",{children:[v.jsx("h1",{className:"text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent",children:"Dashboard Vendeur"}),v.jsxs("p",{className:"text-gray-600 mt-2",children:["Bienvenue, ",d==null?void 0:d.name," - ",d==null?void 0:d.business]})]}),v.jsxs(le.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"flex items-center space-x-2 bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200",children:[v.jsx(mp,{className:"w-5 h-5"}),v.jsx("span",{children:"Nouveau Produit"})]})]}),v.jsxs("div",{className:"flex flex-wrap gap-4",children:[v.jsx(w,{tab:"overview",label:"Vue d'ensemble",icon:v.jsx(fp,{className:"w-5 h-5"})}),v.jsx(w,{tab:"orders",label:"Commandes",icon:v.jsx(gp,{className:"w-5 h-5"})}),v.jsx(w,{tab:"products",label:"Produits",icon:v.jsx(hp,{className:"w-5 h-5"})}),v.jsx(w,{tab:"analytics",label:"Analyses",icon:v.jsx(yp,{className:"w-5 h-5"})})]})]}),v.jsxs(le.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:[a==="overview"&&v.jsxs("div",{className:"space-y-8",children:[v.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[{title:"Commandes totales",value:p.totalOrders,icon:v.jsx(gp,{className:"w-8 h-8"}),color:"from-blue-500 to-blue-600",bgColor:"bg-blue-100",change:"+12%"},{title:"Chiffre d'affaires",value:x(p.totalRevenue),icon:v.jsx(hp,{className:"w-8 h-8"}),color:"from-green-500 to-green-600",bgColor:"bg-green-100",change:"+8%"},{title:"Produits actifs",value:p.activeProducts,icon:v.jsx(fp,{className:"w-8 h-8"}),color:"from-purple-500 to-purple-600",bgColor:"bg-purple-100",change:"+3"},{title:"Note moyenne",value:`${p.averageRating}/5`,icon:v.jsx(yp,{className:"w-8 h-8"}),color:"from-orange-500 to-orange-600",bgColor:"bg-orange-100",change:"+0.2"}].map((S,A)=>v.jsxs(le.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:A*.1},whileHover:{y:-5},className:"bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300",children:[v.jsxs("div",{className:"flex items-center justify-between mb-4",children:[v.jsx("div",{className:`p-3 rounded-xl ${S.bgColor}`,children:v.jsx("div",{className:`bg-gradient-to-r ${S.color} p-2 rounded-lg text-white`,children:S.icon})}),v.jsx("span",{className:"text-sm font-medium text-green-600",children:S.change})]}),v.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-1",children:S.value}),v.jsx("p",{className:"text-sm text-gray-600",children:S.title})]},A))}),v.jsxs("div",{className:"bg-white rounded-2xl shadow-lg border border-gray-100",children:[v.jsx("div",{className:"p-6 border-b border-gray-200",children:v.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"Commandes récentes"})}),v.jsx("div",{className:"p-6",children:v.jsx("div",{className:"space-y-4",children:f.slice(0,5).map(S=>v.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-xl",children:[v.jsxs("div",{className:"flex items-center space-x-4",children:[v.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold",children:S.orderNumber.slice(-2)}),v.jsxs("div",{children:[v.jsx("p",{className:"font-semibold text-gray-900",children:S.orderNumber}),v.jsxs("p",{className:"text-sm text-gray-600",children:[S.items.length," article(s)"]})]})]}),v.jsxs("div",{className:"text-right",children:[v.jsx("p",{className:"font-bold text-gray-900",children:x(S.total)}),v.jsx("span",{className:`inline-block px-2 py-1 rounded-full text-xs font-medium ${g(S.status)}`,children:y(S.status)})]})]},S.id))})})]})]}),a==="orders"&&v.jsxs("div",{className:"space-y-6",children:[v.jsx("div",{className:"flex flex-wrap gap-3",children:[{label:"Toutes",count:p.totalOrders,color:"bg-gray-100 text-gray-800"},{label:"En attente",count:p.pendingOrders,color:"bg-yellow-100 text-yellow-800"},{label:"Terminées",count:p.completedOrders,color:"bg-green-100 text-green-800"}].map(S=>v.jsxs("button",{className:`px-4 py-2 rounded-lg font-medium transition-all duration-200 hover:shadow-md ${S.color}`,children:[S.label," (",S.count,")"]},S.label))}),v.jsxs("div",{className:"bg-white rounded-2xl shadow-lg border border-gray-100",children:[v.jsx("div",{className:"p-6 border-b border-gray-200",children:v.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"Toutes les commandes"})}),v.jsx("div",{className:"divide-y divide-gray-200",children:f.map(S=>v.jsxs(le.div,{initial:{opacity:0},animate:{opacity:1},className:"p-6 hover:bg-gray-50 transition-colors duration-200",children:[v.jsxs("div",{className:"flex items-center justify-between mb-4",children:[v.jsxs("div",{className:"flex items-center space-x-4",children:[v.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center text-white font-bold text-lg",children:S.orderNumber.slice(-2)}),v.jsxs("div",{children:[v.jsx("h4",{className:"text-lg font-semibold text-gray-900",children:S.orderNumber}),v.jsx("p",{className:"text-sm text-gray-600",children:new Date(S.orderDate).toLocaleDateString("fr-FR",{day:"numeric",month:"long",year:"numeric",hour:"2-digit",minute:"2-digit"})})]})]}),v.jsxs("div",{className:"text-right",children:[v.jsx("p",{className:"text-2xl font-bold text-gray-900",children:x(S.total)}),v.jsx("span",{className:`inline-block px-3 py-1 rounded-full text-sm font-medium ${g(S.status)}`,children:y(S.status)})]})]}),v.jsxs("div",{className:"mb-4",children:[v.jsx("h5",{className:"font-medium text-gray-900 mb-2",children:"Articles commandés :"}),v.jsx("div",{className:"space-y-2",children:S.items.map((A,D)=>v.jsxs("div",{className:"flex justify-between text-sm",children:[v.jsxs("span",{className:"text-gray-600",children:[A.quantity,"x ",A.name]}),v.jsx("span",{className:"font-medium",children:x(A.total)})]},D))})]}),v.jsxs("div",{className:"flex flex-wrap gap-2",children:[v.jsxs(le.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200",children:[v.jsx(m0,{className:"w-4 h-4"}),v.jsx("span",{children:"Voir détails"})]}),S.status==="pending"&&v.jsxs(le.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"flex items-center space-x-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors duration-200",children:[v.jsx(YS,{className:"w-4 h-4"}),v.jsx("span",{children:"Confirmer"})]}),S.status==="confirmed"&&v.jsxs(le.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"flex items-center space-x-2 px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors duration-200",children:[v.jsx(p0,{className:"w-4 h-4"}),v.jsx("span",{children:"Marquer prêt"})]}),(S.status==="prepared"||S.status==="in_production")&&v.jsxs(le.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"flex items-center space-x-2 px-4 py-2 bg-cyan-500 text-white rounded-lg hover:bg-cyan-600 transition-colors duration-200",children:[v.jsx(g0,{className:"w-4 h-4"}),v.jsx("span",{children:"Expédier"})]})]})]},S.id))})]})]}),a==="products"&&v.jsx("div",{className:"space-y-6",children:v.jsxs("div",{className:"bg-white rounded-2xl shadow-lg border border-gray-100",children:[v.jsx("div",{className:"p-6 border-b border-gray-200",children:v.jsxs("div",{className:"flex items-center justify-between",children:[v.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"Mes produits"}),v.jsxs(le.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"flex items-center space-x-2 bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-2 rounded-lg",children:[v.jsx(mp,{className:"w-4 h-4"}),v.jsx("span",{children:"Ajouter un produit"})]})]})}),v.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6",children:h.map(S=>v.jsxs(le.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},whileHover:{y:-5},className:"bg-gray-50 rounded-xl p-4 border border-gray-200 hover:border-blue-300 transition-all duration-200",children:[v.jsx("img",{src:S.image,alt:S.name,className:"w-full h-32 object-cover rounded-lg mb-4"}),v.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:S.name}),v.jsx("p",{className:"text-sm text-gray-600 mb-3 line-clamp-2",children:S.description}),v.jsxs("div",{className:"flex items-center justify-between mb-3",children:[v.jsx("span",{className:"text-lg font-bold text-gray-900",children:x(S.price)}),v.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${S.isAvailable?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:S.isAvailable?"Disponible":"Indisponible"})]}),v.jsxs("div",{className:"flex space-x-2",children:[v.jsx("button",{className:"flex-1 px-3 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors duration-200",children:"Modifier"}),v.jsx("button",{className:"px-3 py-2 bg-gray-200 text-gray-700 text-sm rounded-lg hover:bg-gray-300 transition-colors duration-200",children:S.isAvailable?"Désactiver":"Activer"})]})]},S.id))})]})}),a==="analytics"&&v.jsx("div",{className:"space-y-6",children:v.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[v.jsxs("div",{className:"bg-white rounded-2xl shadow-lg border border-gray-100 p-6",children:[v.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"Évolution des ventes"}),v.jsx("div",{className:"h-64 bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl flex items-center justify-center",children:v.jsx("p",{className:"text-gray-500",children:"Graphique des ventes (simulation)"})})]}),v.jsxs("div",{className:"bg-white rounded-2xl shadow-lg border border-gray-100 p-6",children:[v.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"Produits populaires"}),v.jsx("div",{className:"space-y-3",children:h.slice(0,5).map((S,A)=>v.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[v.jsxs("div",{className:"flex items-center space-x-3",children:[v.jsx("span",{className:"w-6 h-6 bg-blue-500 text-white text-sm rounded-full flex items-center justify-center",children:A+1}),v.jsx("span",{className:"font-medium text-gray-900",children:S.name})]}),v.jsxs("span",{className:"text-sm text-gray-600",children:[S.rating," ⭐"]})]},S.id))})]})]})})]},a)]})})},S5=()=>{const{state:n}=yo();return n.isLoading?v.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-orange-50 flex items-center justify-center",children:v.jsxs("div",{className:"text-center",children:[v.jsx("div",{className:"relative w-16 h-16 mx-auto mb-4",children:v.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500 to-orange-500 rounded-full animate-spin",children:v.jsx("div",{className:"absolute inset-2 bg-white rounded-full"})})}),v.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Chargement en cours..."}),v.jsx("p",{className:"text-gray-600",children:"Préparation de votre marketplace local"})]})}):n.error?v.jsx("div",{className:"min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 flex items-center justify-center",children:v.jsxs("div",{className:"text-center max-w-md mx-auto p-8",children:[v.jsx("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:v.jsx("span",{className:"text-2xl",children:"⚠️"})}),v.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Erreur de chargement"}),v.jsx("p",{className:"text-gray-600 mb-4",children:n.error}),v.jsx("button",{onClick:()=>window.location.reload(),className:"bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors duration-200",children:"Réessayer"})]})}):v.jsx(fv,{children:v.jsxs("div",{className:"min-h-screen bg-white",children:[v.jsx(w5,{}),v.jsx("main",{children:v.jsxs(uv,{children:[v.jsx(Zs,{path:"/",element:n.userRole==="customer"?v.jsx(xp,{}):v.jsx(wp,{})}),v.jsx(Zs,{path:"/customer",element:v.jsx(xp,{})}),v.jsx(Zs,{path:"/vendor",element:v.jsx(wp,{})})]})})]})})};function E5(){return v.jsx(Cp,{children:v.jsxs(s1,{children:[v.jsx(S5,{}),v.jsx(n1,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff",borderRadius:"10px"},success:{style:{background:"#10B981"}},error:{style:{background:"#EF4444"}}}})]})})}Cy.createRoot(document.getElementById("root")).render(v.jsx(C.StrictMode,{children:v.jsx(Cp,{children:v.jsx(E5,{})})}));
